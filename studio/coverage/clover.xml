<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1747740213058" clover="3.2.0">
  <project timestamp="1747740213058" name="All files">
    <metrics statements="613" coveredstatements="459" conditionals="632" coveredconditionals="403" methods="126" coveredmethods="93" elements="1371" coveredelements="955" complexity="0" loc="613" ncloc="613" packages="2" files="7" classes="7"/>
    <package name="screens">
      <metrics statements="567" coveredstatements="415" conditionals="626" coveredconditionals="397" methods="117" coveredmethods="84"/>
      <file name="ConfigureMatchScreen.tsx" path="/Users/<USER>/Documents/studio-master2/GullyScoreExpo/src/screens/ConfigureMatchScreen.tsx">
        <metrics statements="99" coveredstatements="94" conditionals="92" coveredconditionals="77" methods="26" coveredmethods="26"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="20" count="1" type="stmt"/>
        <line num="25" count="52" type="cond" truecount="2" falsecount="0"/>
        <line num="26" count="52" type="stmt"/>
        <line num="28" count="52" type="cond" truecount="2" falsecount="0"/>
        <line num="29" count="52" type="cond" truecount="2" falsecount="0"/>
        <line num="32" count="52" type="cond" truecount="2" falsecount="0"/>
        <line num="33" count="52" type="stmt"/>
        <line num="35" count="52" type="cond" truecount="2" falsecount="0"/>
        <line num="36" count="52" type="cond" truecount="2" falsecount="0"/>
        <line num="43" count="1" type="stmt"/>
        <line num="45" count="52" type="cond" truecount="2" falsecount="0"/>
        <line num="46" count="43" type="stmt"/>
        <line num="54" count="52" type="cond" truecount="4" falsecount="0"/>
        <line num="55" count="8" type="stmt"/>
        <line num="63" count="90" type="stmt"/>
        <line num="64" count="52" type="cond" truecount="2" falsecount="0"/>
        <line num="65" count="37" type="stmt"/>
        <line num="73" count="90" type="stmt"/>
        <line num="74" count="52" type="cond" truecount="2" falsecount="0"/>
        <line num="75" count="36" type="stmt"/>
        <line num="90" count="87" type="stmt"/>
        <line num="91" count="87" type="stmt"/>
        <line num="100" count="87" type="stmt"/>
        <line num="113" count="87" type="stmt"/>
        <line num="114" count="87" type="stmt"/>
        <line num="115" count="87" type="stmt"/>
        <line num="117" count="87" type="stmt"/>
        <line num="118" count="20" type="stmt"/>
        <line num="120" count="20" type="stmt"/>
        <line num="122" count="20" type="cond" truecount="5" falsecount="0"/>
        <line num="123" count="2" type="stmt"/>
        <line num="124" count="2" type="stmt"/>
        <line num="125" count="2" type="stmt"/>
        <line num="126" count="2" type="stmt"/>
        <line num="129" count="18" type="stmt"/>
        <line num="130" count="36" type="stmt"/>
        <line num="131" count="36" type="cond" truecount="2" falsecount="0"/>
        <line num="132" count="4" type="stmt"/>
        <line num="133" count="34" type="cond" truecount="2" falsecount="0"/>
        <line num="134" count="6" type="stmt"/>
        <line num="136" count="36" type="stmt"/>
        <line num="140" count="18" type="cond" truecount="1" falsecount="1"/>
        <line num="141" count="18" type="cond" truecount="1" falsecount="1"/>
        <line num="143" count="18" type="stmt"/>
        <line num="144" count="18" type="stmt"/>
        <line num="146" count="18" type="cond" truecount="1" falsecount="1"/>
        <line num="147" count="18" type="stmt"/>
        <line num="148" count="18" type="stmt"/>
        <line num="152" count="18" type="stmt"/>
        <line num="156" count="87" type="stmt"/>
        <line num="158" count="3" type="stmt"/>
        <line num="160" count="3" type="stmt"/>
        <line num="161" count="8" type="stmt"/>
        <line num="169" count="3" type="stmt"/>
        <line num="170" count="3" type="stmt"/>
        <line num="172" count="3" type="stmt"/>
        <line num="174" count="3" type="stmt"/>
        <line num="188" count="3" type="stmt"/>
        <line num="189" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="190" count="2" type="stmt"/>
        <line num="192" count="1" type="stmt"/>
        <line num="197" count="87" type="stmt"/>
        <line num="198" count="9" type="stmt"/>
        <line num="200" count="9" type="stmt"/>
        <line num="201" count="9" type="cond" truecount="1" falsecount="1"/>
        <line num="203" count="9" type="stmt"/>
        <line num="205" count="9" type="cond" truecount="1" falsecount="1"/>
        <line num="206" count="9" type="cond" truecount="2" falsecount="0"/>
        <line num="207" count="7" type="stmt"/>
        <line num="208" count="2" type="cond" truecount="3" falsecount="1"/>
        <line num="209" count="2" type="stmt"/>
        <line num="210" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="220" count="0" type="stmt"/>
        <line num="226" count="87" type="stmt"/>
        <line num="227" count="174" type="cond" truecount="2" falsecount="0"/>
        <line num="228" count="174" type="cond" truecount="2" falsecount="0"/>
        <line num="229" count="174" type="stmt"/>
        <line num="231" count="174" type="cond" truecount="5" falsecount="0"/>
        <line num="232" count="16" type="stmt"/>
        <line num="236" count="158" type="cond" truecount="1" falsecount="1"/>
        <line num="237" count="0" type="stmt"/>
        <line num="242" count="158" type="cond" truecount="4" falsecount="0"/>
        <line num="246" count="8" type="stmt"/>
        <line num="249" count="150" type="stmt"/>
        <line num="250" count="278" type="stmt"/>
        <line num="255" count="293" type="stmt"/>
        <line num="272" count="87" type="stmt"/>
        <line num="286" count="97" type="stmt"/>
        <line num="300" count="97" type="stmt"/>
        <line num="314" count="95" type="stmt"/>
        <line num="328" count="89" type="stmt"/>
        <line num="342" count="92" type="cond" truecount="2" falsecount="0"/>
        <line num="367" count="1" type="stmt"/>
      </file>
      <file name="HomeScreen.tsx" path="/Users/<USER>/Documents/studio-master2/GullyScoreExpo/src/screens/HomeScreen.tsx">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="3"/>
        <line num="7" count="1" type="stmt"/>
        <line num="14" count="3" type="stmt"/>
        <line num="16" count="3" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="32" count="1" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
      </file>
      <file name="MatchHistoryScreen.tsx" path="/Users/<USER>/Documents/studio-master2/GullyScoreExpo/src/screens/MatchHistoryScreen.tsx">
        <metrics statements="31" coveredstatements="31" conditionals="14" coveredconditionals="10" methods="10" coveredmethods="9"/>
        <line num="16" count="15" type="stmt"/>
        <line num="17" count="15" type="stmt"/>
        <line num="18" count="15" type="stmt"/>
        <line num="20" count="15" type="stmt"/>
        <line num="21" count="7" type="stmt"/>
        <line num="22" count="7" type="stmt"/>
        <line num="23" count="7" type="stmt"/>
        <line num="24" count="7" type="stmt"/>
        <line num="26" count="6" type="cond" truecount="2" falsecount="2"/>
        <line num="27" count="6" type="stmt"/>
        <line num="29" count="1" type="stmt"/>
        <line num="30" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="33" count="7" type="stmt"/>
        <line num="37" count="15" type="stmt"/>
        <line num="39" count="7" type="stmt"/>
        <line num="40" count="7" type="stmt"/>
        <line num="44" count="15" type="stmt"/>
        <line num="45" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="46" count="4" type="stmt"/>
        <line num="47" count="4" type="stmt"/>
        <line num="50" count="15" type="stmt"/>
        <line num="51" count="5" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="61" count="15" type="cond" truecount="2" falsecount="0"/>
        <line num="62" count="8" type="stmt"/>
        <line num="70" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="71" count="3" type="stmt"/>
        <line num="78" count="4" type="stmt"/>
        <line num="82" count="9" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
      </file>
      <file name="MatchScoringScreen.tsx" path="/Users/<USER>/Documents/studio-master2/GullyScoreExpo/src/screens/MatchScoringScreen.tsx">
        <metrics statements="331" coveredstatements="236" conditionals="432" coveredconditionals="273" methods="50" coveredmethods="38"/>
        <line num="36" count="1" type="stmt"/>
        <line num="56" count="76" type="cond" truecount="8" falsecount="1"/>
        <line num="58" count="12" type="stmt"/>
        <line num="65" count="11" type="stmt"/>
        <line num="67" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="69" count="11" type="stmt"/>
        <line num="70" count="11" type="stmt"/>
        <line num="72" count="11" type="cond" truecount="1" falsecount="1"/>
        <line num="73" count="11" type="cond" truecount="4" falsecount="0"/>
        <line num="74" count="2" type="stmt"/>
        <line num="75" count="9" type="cond" truecount="1" falsecount="1"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="79" count="0" type="stmt"/>
        <line num="82" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="83" count="9" type="cond" truecount="1" falsecount="1"/>
        <line num="84" count="9" type="stmt"/>
        <line num="86" count="11" type="stmt"/>
        <line num="88" count="1" type="stmt"/>
        <line num="90" count="9" type="stmt"/>
        <line num="92" count="30" type="stmt"/>
        <line num="94" count="7" type="stmt"/>
        <line num="96" count="5" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="106" count="80" type="stmt"/>
        <line num="107" count="80" type="stmt"/>
        <line num="109" count="80" type="stmt"/>
        <line num="111" count="80" type="stmt"/>
        <line num="112" count="80" type="stmt"/>
        <line num="115" count="80" type="stmt"/>
        <line num="116" count="80" type="stmt"/>
        <line num="117" count="80" type="stmt"/>
        <line num="121" count="80" type="cond" truecount="4" falsecount="0"/>
        <line num="122" count="80" type="cond" truecount="4" falsecount="0"/>
        <line num="123" count="80" type="cond" truecount="4" falsecount="0"/>
        <line num="125" count="80" type="stmt"/>
        <line num="126" count="97" type="stmt"/>
        <line num="127" count="80" type="stmt"/>
        <line num="129" count="80" type="stmt"/>
        <line num="130" count="12" type="stmt"/>
        <line num="132" count="12" type="stmt"/>
        <line num="133" count="12" type="stmt"/>
        <line num="134" count="12" type="stmt"/>
        <line num="135" count="12" type="cond" truecount="2" falsecount="0"/>
        <line num="136" count="11" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="146" count="12" type="stmt"/>
        <line num="150" count="80" type="stmt"/>
        <line num="151" count="80" type="cond" truecount="2" falsecount="0"/>
        <line num="152" count="1" type="stmt"/>
        <line num="155" count="1" type="stmt"/>
        <line num="157" count="1" type="stmt"/>
        <line num="161" count="80" type="stmt"/>
        <line num="163" count="64" type="cond" truecount="2" falsecount="0"/>
        <line num="165" count="41" type="cond" truecount="1" falsecount="1"/>
        <line num="168" count="41" type="cond" truecount="6" falsecount="0"/>
        <line num="169" count="7" type="stmt"/>
        <line num="173" count="7" type="stmt"/>
        <line num="174" count="34" type="cond" truecount="8" falsecount="0"/>
        <line num="175" count="3" type="stmt"/>
        <line num="179" count="3" type="stmt"/>
        <line num="184" count="80" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="80" type="stmt"/>
        <line num="192" count="30" type="cond" truecount="1" falsecount="1"/>
        <line num="194" count="30" type="cond" truecount="2" falsecount="0"/>
        <line num="195" count="2" type="stmt"/>
        <line num="197" count="28" type="stmt"/>
        <line num="201" count="30" type="cond" truecount="3" falsecount="1"/>
        <line num="206" count="0" type="stmt"/>
        <line num="214" count="30" type="stmt"/>
        <line num="215" count="30" type="cond" truecount="1" falsecount="1"/>
        <line num="216" count="30" type="stmt"/>
        <line num="217" count="30" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="224" count="80" type="stmt"/>
        <line num="225" count="1" type="cond" truecount="2" falsecount="2"/>
        <line num="226" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="232" count="80" type="stmt"/>
        <line num="233" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="234" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="237" count="1" type="stmt"/>
        <line num="240" count="80" type="stmt"/>
        <line num="241" count="1" type="cond" truecount="4" falsecount="1"/>
        <line num="242" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="246" count="1" type="stmt"/>
        <line num="247" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="248" count="1" type="stmt"/>
        <line num="249" count="1" type="stmt"/>
        <line num="250" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="251" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="254" count="1" type="stmt"/>
        <line num="256" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="257" count="1" type="stmt"/>
        <line num="261" count="80" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="265" count="80" type="stmt"/>
        <line num="266" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="stmt"/>
        <line num="270" count="1" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="272" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="273" count="1" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="275" count="0" type="stmt"/>
        <line num="277" count="1" type="stmt"/>
        <line num="279" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="280" count="1" type="stmt"/>
        <line num="281" count="1" type="stmt"/>
        <line num="285" count="80" type="stmt"/>
        <line num="286" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="287" count="5" type="cond" truecount="4" falsecount="1"/>
        <line num="289" count="5" type="stmt"/>
        <line num="290" count="5" type="stmt"/>
        <line num="291" count="5" type="stmt"/>
        <line num="294" count="80" type="stmt"/>
        <line num="295" count="28" type="cond" truecount="1" falsecount="1"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="297" count="0" type="stmt"/>
        <line num="300" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="302" count="28" type="stmt"/>
        <line num="303" count="28" type="cond" truecount="5" falsecount="1"/>
        <line num="304" count="28" type="cond" truecount="2" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="28" type="cond" truecount="1" falsecount="1"/>
        <line num="310" count="28" type="stmt"/>
        <line num="314" count="28" type="cond" truecount="1" falsecount="1"/>
        <line num="315" count="0" type="stmt"/>
        <line num="317" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="318" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="319" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="320" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="324" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="325" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="333" count="28" type="cond" truecount="3" falsecount="1"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="338" count="28" type="stmt"/>
        <line num="339" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="341" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="342" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="344" count="28" type="stmt"/>
        <line num="345" count="30" type="stmt"/>
        <line num="347" count="28" type="cond" truecount="3" falsecount="1"/>
        <line num="348" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="353" count="28" type="cond" truecount="1" falsecount="1"/>
        <line num="358" count="28" type="stmt"/>
        <line num="359" count="28" type="stmt"/>
        <line num="361" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="362" count="4" type="stmt"/>
        <line num="363" count="4" type="stmt"/>
        <line num="365" count="4" type="stmt"/>
        <line num="367" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="368" count="4" type="stmt"/>
        <line num="370" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="371" count="4" type="stmt"/>
        <line num="374" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="375" count="1" type="cond" truecount="2" falsecount="2"/>
        <line num="376" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="377" count="0" type="stmt"/>
        <line num="378" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="382" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="383" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="389" count="0" type="stmt"/>
        <line num="393" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="394" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="395" count="0" type="stmt"/>
        <line num="396" count="0" type="stmt"/>
        <line num="399" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="404" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="405" count="1" type="stmt"/>
        <line num="409" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="410" count="1" type="stmt"/>
        <line num="411" count="1" type="stmt"/>
        <line num="412" count="1" type="stmt"/>
        <line num="413" count="1" type="stmt"/>
        <line num="414" count="1" type="stmt"/>
        <line num="415" count="23" type="cond" truecount="2" falsecount="0"/>
        <line num="416" count="2" type="stmt"/>
        <line num="417" count="2" type="stmt"/>
        <line num="418" count="2" type="stmt"/>
        <line num="419" count="2" type="stmt"/>
        <line num="420" count="2" type="stmt"/>
        <line num="423" count="21" type="stmt"/>
        <line num="424" count="21" type="stmt"/>
        <line num="425" count="21" type="stmt"/>
        <line num="426" count="21" type="stmt"/>
        <line num="427" count="21" type="stmt"/>
        <line num="428" count="21" type="stmt"/>
        <line num="429" count="21" type="cond" truecount="2" falsecount="0"/>
        <line num="430" count="21" type="cond" truecount="1" falsecount="1"/>
        <line num="433" count="21" type="cond" truecount="4" falsecount="0"/>
        <line num="434" count="5" type="stmt"/>
        <line num="438" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="439" count="25" type="stmt"/>
        <line num="440" count="25" type="stmt"/>
        <line num="441" count="25" type="stmt"/>
        <line num="445" count="28" type="stmt"/>
        <line num="446" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="447" count="3" type="stmt"/>
        <line num="448" count="3" type="stmt"/>
        <line num="449" count="3" type="stmt"/>
        <line num="452" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="453" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="456" count="3" type="stmt"/>
        <line num="458" count="3" type="stmt"/>
        <line num="462" count="28" type="cond" truecount="1" falsecount="1"/>
        <line num="465" count="28" type="stmt"/>
        <line num="468" count="28" type="cond" truecount="5" falsecount="0"/>
        <line num="469" count="1" type="stmt"/>
        <line num="470" count="1" type="stmt"/>
        <line num="471" count="1" type="stmt"/>
        <line num="472" count="1" type="stmt"/>
        <line num="475" count="27" type="cond" truecount="4" falsecount="0"/>
        <line num="476" count="4" type="stmt"/>
        <line num="477" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="478" count="2" type="stmt"/>
        <line num="479" count="2" type="stmt"/>
        <line num="480" count="2" type="stmt"/>
        <line num="491" count="2" type="cond" truecount="3" falsecount="1"/>
        <line num="493" count="2" type="stmt"/>
        <line num="494" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="495" count="2" type="stmt"/>
        <line num="496" count="2" type="stmt"/>
        <line num="497" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="498" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="502" count="2" type="stmt"/>
        <line num="506" count="28" type="cond" truecount="2" falsecount="0"/>
        <line num="507" count="28" type="cond" truecount="3" falsecount="0"/>
        <line num="510" count="28" type="stmt"/>
        <line num="512" count="28" type="cond" truecount="1" falsecount="1"/>
        <line num="513" count="28" type="cond" truecount="4" falsecount="0"/>
        <line num="514" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="515" count="3" type="stmt"/>
        <line num="516" count="3" type="stmt"/>
        <line num="517" count="25" type="cond" truecount="2" falsecount="2"/>
        <line num="518" count="0" type="stmt"/>
        <line num="519" count="25" type="cond" truecount="2" falsecount="0"/>
        <line num="520" count="2" type="stmt"/>
        <line num="521" count="2" type="stmt"/>
        <line num="522" count="2" type="stmt"/>
        <line num="523" count="2" type="stmt"/>
        <line num="525" count="23" type="cond" truecount="2" falsecount="0"/>
        <line num="526" count="23" type="cond" truecount="4" falsecount="0"/>
        <line num="527" count="1" type="cond" truecount="2" falsecount="4"/>
        <line num="532" count="1" type="cond" truecount="2" falsecount="2"/>
        <line num="533" count="1" type="stmt"/>
        <line num="537" count="23" type="cond" truecount="4" falsecount="0"/>
        <line num="538" count="1" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="546" count="80" type="stmt"/>
        <line num="547" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="548" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="549" count="0" type="stmt"/>
        <line num="550" count="0" type="stmt"/>
        <line num="552" count="0" type="stmt"/>
        <line num="553" count="0" type="stmt"/>
        <line num="554" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="555" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="556" count="0" type="stmt"/>
        <line num="558" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
        <line num="561" count="0" type="stmt"/>
        <line num="564" count="80" type="stmt"/>
        <line num="565" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="566" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="567" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="572" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="573" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="574" count="0" type="stmt"/>
        <line num="575" count="0" type="stmt"/>
        <line num="577" count="0" type="stmt"/>
        <line num="579" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="583" count="80" type="cond" truecount="2" falsecount="0"/>
        <line num="584" count="24" type="stmt"/>
        <line num="591" count="56" type="cond" truecount="2" falsecount="0"/>
        <line num="592" count="2" type="stmt"/>
        <line num="600" count="54" type="cond" truecount="1" falsecount="1"/>
        <line num="603" count="0" type="stmt"/>
        <line num="610" count="54" type="cond" truecount="2" falsecount="0"/>
        <line num="611" count="54" type="cond" truecount="1" falsecount="1"/>
        <line num="612" count="54" type="cond" truecount="1" falsecount="4"/>
        <line num="615" count="54" type="stmt"/>
        <line num="616" count="41" type="stmt"/>
        <line num="658" count="164" type="stmt"/>
        <line num="663" count="164" type="stmt"/>
        <line num="667" count="4" type="stmt"/>
        <line num="673" count="54" type="stmt"/>
        <line num="695" count="12" type="stmt"/>
        <line num="698" count="1" type="stmt"/>
        <line num="707" count="12" type="stmt"/>
        <line num="710" count="1" type="stmt"/>
        <line num="729" count="4" type="stmt"/>
        <line num="732" count="1" type="stmt"/>
        <line num="750" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="752" count="1" type="stmt"/>
        <line num="755" count="0" type="stmt"/>
        <line num="760" count="3" type="cond" truecount="3" falsecount="1"/>
        <line num="773" count="2" type="stmt"/>
        <line num="776" count="0" type="stmt"/>
        <line num="793" count="0" type="stmt"/>
        <line num="800" count="1" type="stmt"/>
      </file>
      <file name="MatchSummaryScreen.tsx" path="/Users/<USER>/Documents/studio-master2/GullyScoreExpo/src/screens/MatchSummaryScreen.tsx">
        <metrics statements="51" coveredstatements="0" conditionals="43" coveredconditionals="0" methods="20" coveredmethods="0"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="59" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="76" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="124" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="141" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
      </file>
      <file name="TossScreen.tsx" path="/Users/<USER>/Documents/studio-master2/GullyScoreExpo/src/screens/TossScreen.tsx">
        <metrics statements="49" coveredstatements="48" conditionals="45" coveredconditionals="37" methods="8" coveredmethods="8"/>
        <line num="14" count="23" type="stmt"/>
        <line num="15" count="23" type="stmt"/>
        <line num="17" count="23" type="stmt"/>
        <line num="19" count="23" type="stmt"/>
        <line num="20" count="23" type="stmt"/>
        <line num="21" count="23" type="stmt"/>
        <line num="22" count="23" type="stmt"/>
        <line num="24" count="23" type="stmt"/>
        <line num="25" count="7" type="stmt"/>
        <line num="26" count="7" type="stmt"/>
        <line num="27" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="28" count="6" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="34" count="7" type="stmt"/>
        <line num="37" count="23" type="stmt"/>
        <line num="38" count="26" type="cond" truecount="2" falsecount="0"/>
        <line num="39" count="19" type="cond" truecount="2" falsecount="0"/>
        <line num="40" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="41" count="6" type="stmt"/>
        <line num="44" count="23" type="stmt"/>
        <line num="45" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="46" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="47" count="5" type="stmt"/>
        <line num="48" count="5" type="stmt"/>
        <line num="49" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="50" count="5" type="stmt"/>
        <line num="53" count="23" type="stmt"/>
        <line num="54" count="3" type="cond" truecount="3" falsecount="1"/>
        <line num="55" count="3" type="stmt"/>
        <line num="59" count="3" type="stmt"/>
        <line num="60" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="62" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="63" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="64" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="66" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="67" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="70" count="3" type="stmt"/>
        <line num="90" count="3" type="stmt"/>
        <line num="91" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="92" count="2" type="stmt"/>
        <line num="93" count="2" type="stmt"/>
        <line num="95" count="1" type="stmt"/>
        <line num="99" count="23" type="stmt"/>
        <line num="101" count="23" type="cond" truecount="2" falsecount="0"/>
        <line num="102" count="7" type="stmt"/>
        <line num="109" count="16" type="stmt"/>
        <line num="125" count="2" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="46" coveredstatements="44" conditionals="6" coveredconditionals="6" methods="9" coveredmethods="9"/>
      <file name="storage.ts" path="/Users/<USER>/Documents/studio-master2/GullyScoreExpo/src/utils/storage.ts">
        <metrics statements="46" coveredstatements="44" conditionals="6" coveredconditionals="6" methods="9" coveredmethods="9"/>
        <line num="4" count="3" type="stmt"/>
        <line num="7" count="3" type="stmt"/>
        <line num="8" count="15" type="stmt"/>
        <line num="9" count="15" type="stmt"/>
        <line num="10" count="15" type="cond" truecount="2" falsecount="0"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="18" count="3" type="stmt"/>
        <line num="19" count="5" type="stmt"/>
        <line num="20" count="5" type="stmt"/>
        <line num="21" count="5" type="stmt"/>
        <line num="23" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="25" count="1" type="stmt"/>
        <line num="28" count="4" type="stmt"/>
        <line num="30" count="5" type="stmt"/>
        <line num="31" count="5" type="stmt"/>
        <line num="32" count="4" type="stmt"/>
        <line num="33" count="4" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="36" count="1" type="stmt"/>
        <line num="41" count="3" type="stmt"/>
        <line num="42" count="4" type="stmt"/>
        <line num="43" count="4" type="stmt"/>
        <line num="44" count="4" type="stmt"/>
        <line num="45" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="53" count="3" type="stmt"/>
        <line num="54" count="3" type="stmt"/>
        <line num="58" count="3" type="stmt"/>
        <line num="59" count="3" type="stmt"/>
        <line num="60" count="3" type="stmt"/>
        <line num="61" count="4" type="stmt"/>
        <line num="62" count="3" type="stmt"/>
        <line num="63" count="3" type="stmt"/>
        <line num="64" count="2" type="stmt"/>
        <line num="65" count="2" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="73" count="3" type="stmt"/>
        <line num="74" count="2" type="stmt"/>
        <line num="75" count="2" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
