
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for screens</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> screens</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70.77% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>436/616</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">63.41% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>397/626</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">71.79% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>84/117</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">73.19% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>415/567</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="ConfigureMatchScreen.tsx"><a href="ConfigureMatchScreen.tsx.html">ConfigureMatchScreen.tsx</a></td>
	<td data-value="94.28" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 94%"></div><div class="cover-empty" style="width: 6%"></div></div>
	</td>
	<td data-value="94.28" class="pct high">94.28%</td>
	<td data-value="105" class="abs high">99/105</td>
	<td data-value="83.69" class="pct high">83.69%</td>
	<td data-value="92" class="abs high">77/92</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="26" class="abs high">26/26</td>
	<td data-value="94.94" class="pct high">94.94%</td>
	<td data-value="99" class="abs high">94/99</td>
	</tr>

<tr>
	<td class="file high" data-value="HomeScreen.tsx"><a href="HomeScreen.tsx.html">HomeScreen.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	</tr>

<tr>
	<td class="file high" data-value="MatchHistoryScreen.tsx"><a href="MatchHistoryScreen.tsx.html">MatchHistoryScreen.tsx</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="34" class="abs high">34/34</td>
	<td data-value="71.42" class="pct medium">71.42%</td>
	<td data-value="14" class="abs medium">10/14</td>
	<td data-value="90" class="pct high">90%</td>
	<td data-value="10" class="abs high">9/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="31" class="abs high">31/31</td>
	</tr>

<tr>
	<td class="file medium" data-value="MatchScoringScreen.tsx"><a href="MatchScoringScreen.tsx.html">MatchScoringScreen.tsx</a></td>
	<td data-value="69.29" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 69%"></div><div class="cover-empty" style="width: 31%"></div></div>
	</td>
	<td data-value="69.29" class="pct medium">69.29%</td>
	<td data-value="355" class="abs medium">246/355</td>
	<td data-value="63.19" class="pct medium">63.19%</td>
	<td data-value="432" class="abs medium">273/432</td>
	<td data-value="76" class="pct medium">76%</td>
	<td data-value="50" class="abs medium">38/50</td>
	<td data-value="71.29" class="pct medium">71.29%</td>
	<td data-value="331" class="abs medium">236/331</td>
	</tr>

<tr>
	<td class="file low" data-value="MatchSummaryScreen.tsx"><a href="MatchSummaryScreen.tsx.html">MatchSummaryScreen.tsx</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="61" class="abs low">0/61</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="43" class="abs low">0/43</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="51" class="abs low">0/51</td>
	</tr>

<tr>
	<td class="file high" data-value="TossScreen.tsx"><a href="TossScreen.tsx.html">TossScreen.tsx</a></td>
	<td data-value="92.72" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 92%"></div><div class="cover-empty" style="width: 8%"></div></div>
	</td>
	<td data-value="92.72" class="pct high">92.72%</td>
	<td data-value="55" class="abs high">51/55</td>
	<td data-value="82.22" class="pct high">82.22%</td>
	<td data-value="45" class="abs high">37/45</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="97.95" class="pct high">97.95%</td>
	<td data-value="49" class="abs high">48/49</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-20T11:23:33.045Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    