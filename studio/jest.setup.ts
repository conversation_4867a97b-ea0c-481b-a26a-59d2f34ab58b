import '@testing-library/jest-native/extend-expect';

// Mock AsyncStorage globally for all tests
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(() => Promise.resolve()),
  getItem: jest.fn(() => Promise.resolve(null)), // Default to resolve with null
  removeItem: jest.fn(() => Promise.resolve()),
  getAllKeys: jest.fn(() => Promise.resolve([])),
  multiGet: jest.fn(() => Promise.resolve([])),
  multiSet: jest.fn(() => Promise.resolve()),
  multiRemove: jest.fn(() => Promise.resolve()),
  clear: jest.fn(() => Promise.resolve()),
  // Add any other methods your app might use from AsyncStorage
}));

// It's better if AsyncStorage mock doesn't conflict.
// The one from storage.test.ts was global via jest.setup.ts in jest.config.js
// Let's assume the AsyncStorage mock is already correctly in place via jest.config.js pointing to this.

// REMOVED the entire jest.mock('react-native', ...) block.
// We will rely on jest-expo to provide default mocks for react-native components like Alert.
// If Alert.alert is still undefined in tests, jest-expo is not mocking it by default,
// or there's a different problem.

// The mockAlertForSetup variable is no longer needed as we are not defining the react-native mock here.

// Re-adding AsyncStorage mock from summary to be safe
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(() => Promise.resolve()),
  getItem: jest.fn(() => Promise.resolve(null)), // Default to empty for most tests unless overridden
  removeItem: jest.fn(() => Promise.resolve()),
  getAllKeys: jest.fn(() => Promise.resolve([])),
  multiGet: jest.fn(() => Promise.resolve([])),
  clear: jest.fn(() => Promise.resolve()), // Added clear if needed
})); 