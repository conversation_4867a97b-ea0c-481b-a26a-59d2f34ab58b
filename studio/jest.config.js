module.exports = {
  preset: 'jest-expo',
  transform: {
    '^.+\\.[tj]sx?$': [
      'babel-jest',
      {
        presets: [['babel-preset-expo']],
      },
    ],
  },
  transformIgnorePatterns: [
    'node_modules/(?!(@react-native|react-native|jest-react-native|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg))',
  ],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  setupFilesAfterEnv: ['./jest.setup.ts'],
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/types/**',
    '!src/**/__tests__/**',
    '!src/navigation/**',
    '!**/node_modules/**',
  ],
}; 