export type PlayerBattingStats = {
  runsScored: number;
  ballsFaced: number;
  fours: number;
  sixes: number;
  status: 'not out' | 'bowled' | 'caught' | 'lbw' | 'run out' | 'stumped' | 'hit wicket' | 'retired hurt' | 'did not bat' | 'out';
  // Add other relevant batting stats if needed, e.g., strikeRate, howOutBy (bowlerId)
};

export type PlayerBowlingStats = {
  ballsBowled: number; // Individual balls, not overs.overs * 6 + balls
  runsConceded: number;
  wicketsTaken: number;
  maidensBowled: number;
  widesBowled: number;
  noBallsBowled: number;
  // Add other relevant bowling stats if needed, e.g., economyRate, dotsBowled
};

export type Player = {
  id: string; // Unique identifier for the player (e.g., teamA_player_0_timestamp)
  name: string;
  battingStats: PlayerBattingStats;
  bowlingStats: PlayerBowlingStats;
  isCaptain?: boolean; // Optional
  isWicketKeeper?: boolean; // Optional
};

// Enum for different types of ball outcomes - MOVED HERE
export enum BallOutcome {
  DOT = 0,
  SINGLE = 1,
  DOUBLE = 2,
  TRIPLE = 3,
  FOUR = 4,
  SIX = 6,
  WIDE = 'WIDE',
  NO_BALL = 'NO_BALL',
  WICKET = 'WICKET'
}

// Function to generate a unique player ID
export const generatePlayerId = (teamPrefix: string, playerIndex: number): string => {
  return `${teamPrefix}_player_${playerIndex}_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`;
};

// Function to get initial batting stats for a player
export const initialPlayerBattingStats = (): PlayerBattingStats => ({
  runsScored: 0,
  ballsFaced: 0,
  fours: 0,
  sixes: 0,
  status: 'did not bat',
});

// Function to get initial bowling stats for a player
export const initialPlayerBowlingStats = (): PlayerBowlingStats => ({
  ballsBowled: 0,
  runsConceded: 0,
  wicketsTaken: 0,
  maidensBowled: 0,
  widesBowled: 0,
  noBallsBowled: 0,
});

export type Innings = {
  teamName: string;
  players: Player[]; // Full player list for this innings
  score: number;
  wickets: number;
  oversBowled: number; // e.g., 19.5 for 19 overs and 5 balls
  // further details if needed, like fall of wickets etc.
};

export type Match = {
  id: string; // Unique match identifier
  createdAt: number; // Timestamp
  updatedAt: number; // Timestamp
  status: 'pending' | 'live_inning1' | 'live_inning2' | 'completed' | 'abandoned';

  // Configuration
  teamAName: string;
  teamBName: string;
  oversPerInnings: number;
  playersPerTeam: number;
  lastManStanding: boolean;
  teamAPlayers: Player[];
  teamBPlayers: Player[];

  // Toss Details
  tossWinnerTeamName?: string;
  decision?: 'Bat' | 'Bowl';
  
  // Live State - Innings 1
  firstInnings?: {
    battingTeamName: string;
    bowlingTeamName: string;
    score: number;
    wickets: number;
    oversCompleted: number; // Full overs completed
    ballsInCurrentOver: number;
    currentOverHistory: string[];
    runsConcededThisOverMaiden: number; // For maiden calc, runs off bat/byes/lbyes
    strikerId?: string | null;
    nonStrikerId?: string | null;
    currentBowlerId?: string | null;
    timeline: BallOutcome[]; // Optional: detailed log of each ball
    // teamAPlayers and teamBPlayers will have their stats updated directly within them
  };

  // Live State - Innings 2
  secondInnings?: {
    battingTeamName: string;
    bowlingTeamName: string;
    score: number;
    wickets: number;
    oversCompleted: number;
    ballsInCurrentOver: number;
    currentOverHistory: string[];
    runsConcededThisOverMaiden: number;
    strikerId?: string | null;
    nonStrikerId?: string | null;
    currentBowlerId?: string | null;
    target: number;
    timeline: BallOutcome[];
  };
  
  // Result
  matchWinnerTeamName?: string;
  resultDescription?: string; // e.g., "Team A won by 5 wickets"
};

// We can expand this later to a full Match type
// export type Match = { ... }; 