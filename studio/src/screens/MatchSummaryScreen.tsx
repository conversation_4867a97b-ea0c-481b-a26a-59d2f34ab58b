import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, ActivityIndicator, Button, Alert } from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { RootStackParamList, ScreenNavigationProp } from '../navigation/AppNavigator';
import { Match, Player, PlayerBattingStats, PlayerBowlingStats } from '../types/matchTypes';
import { loadMatch } from '../utils/storage';

type MatchSummaryScreenRouteProp = RouteProp<RootStackParamList, 'MatchSummary'>;

export default function MatchSummaryScreen() {
  const route = useRoute<MatchSummaryScreenRouteProp>();
  const navigation = useNavigation<ScreenNavigationProp<'Home'>>(); // To navigate home
  const { matchId } = route.params;

  const [match, setMatch] = useState<Match | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchMatch = async () => {
      setIsLoading(true);
      const loadedMatch = await loadMatch(matchId);
      if (loadedMatch && loadedMatch.status === 'completed') {
        setMatch(loadedMatch);
      } else {
        // Handle error or match not completed case
        console.error('Error loading completed match or match not found. Match ID:', matchId, 'Loaded Match Status:', loadedMatch?.status, 'Loaded Match:', loadedMatch);
        Alert.alert("Error", `Could not load completed match data (ID: ${matchId}). Status: ${loadedMatch?.status || 'Not Found'}`);
      }
      setIsLoading(false);
    };
    fetchMatch();
  }, [matchId]);

  const renderPlayerBattingStats = (player: Player) => {
    const sr = player.battingStats.ballsFaced > 0 
      ? ((player.battingStats.runsScored / player.battingStats.ballsFaced) * 100).toFixed(2) 
      : '0.00';
    return (
      <View key={player.id} style={styles.playerStatRow}>
        <Text style={styles.playerName}>{player.name}</Text>
        <Text style={styles.statValueNarrow}>{player.battingStats.status === 'not out' ? `${player.battingStats.runsScored}*` : player.battingStats.runsScored}</Text>
        <Text style={styles.statValueNarrow}>{player.battingStats.ballsFaced}</Text>
        <Text style={styles.statValueNarrow}>{player.battingStats.fours}</Text>
        <Text style={styles.statValueNarrow}>{player.battingStats.sixes}</Text>
        <Text style={styles.statValueWide}>{sr}</Text>
      </View>
    );
  };

  const renderPlayerBowlingStats = (player: Player) => {
    const overs = Math.floor(player.bowlingStats.ballsBowled / 6);
    const balls = player.bowlingStats.ballsBowled % 6;
    const econ = player.bowlingStats.ballsBowled > 0 
      ? (player.bowlingStats.runsConceded / (player.bowlingStats.ballsBowled / 6)).toFixed(2) 
      : '0.00';
    // Handle case where ballsBowled is 0 to prevent NaN for econ if runsConceded is also 0.
    const displayEcon = player.bowlingStats.ballsBowled > 0 ? econ : '0.00';

    return (
      <View key={player.id} style={styles.playerStatRow}>
        <Text style={styles.playerName}>{player.name}</Text>
        <Text style={styles.statValueNarrow}>{`${overs}.${balls}`}</Text>
        <Text style={styles.statValueNarrow}>{player.bowlingStats.maidensBowled}</Text>
        <Text style={styles.statValueNarrow}>{player.bowlingStats.runsConceded}</Text>
        <Text style={styles.statValueNarrow}>{player.bowlingStats.wicketsTaken}</Text>
        <Text style={styles.statValueWide}>{displayEcon}</Text>
      </View>
    );
  };

  if (isLoading) {
    return <ActivityIndicator size="large" style={styles.loader} />;
  }

  if (!match) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>Completed match data not found.</Text>
        <Button title="Go Home" onPress={() => navigation.navigate('Home')} />
      </View>
    );
  }

  const { firstInnings, secondInnings } = match;

  const getTeamPlayers = (teamName: string): Player[] => {
    if (teamName === match.teamAName) return match.teamAPlayers;
    if (teamName === match.teamBName) return match.teamBPlayers;
    return [];
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>Match Summary</Text>

      <View style={styles.matchConfigSection}>
        <Text style={styles.configTitle}>Match Configuration</Text>
        <Text style={styles.configItem}>Overs per Innings: {match.oversPerInnings}</Text>
        <Text style={styles.configItem}>Players per Team: {match.playersPerTeam}</Text>
        <Text style={styles.configItem}>Last Man Standing: {match.lastManStanding ? 'Enabled' : 'Disabled'}</Text>
        {match.tossWinnerTeamName && match.decision && (
          <Text style={styles.configItem}>
            Toss: {match.tossWinnerTeamName} won and chose to {match.decision?.toLowerCase()}.
          </Text>
        )}
      </View>

      <Text style={styles.matchResultText}>{match.resultDescription}</Text>

      {firstInnings && (
        <View style={styles.inningsSection}>
          <Text style={styles.inningsTitle}>{firstInnings.battingTeamName} Innings: {firstInnings.score}/{firstInnings.wickets} ({firstInnings.oversCompleted}.{firstInnings.ballsInCurrentOver} Overs)</Text>
          <View style={styles.statsHeader}>
            <Text style={styles.headerPlayerName}>Batsman</Text><Text style={styles.headerStatNarrow}>R</Text><Text style={styles.headerStatNarrow}>B</Text><Text style={styles.headerStatNarrow}>4s</Text><Text style={styles.headerStatNarrow}>6s</Text><Text style={styles.headerStatWide}>SR</Text>
          </View>
          {getTeamPlayers(firstInnings.battingTeamName).filter(p => p.battingStats.ballsFaced > 0 || p.battingStats.status === 'not out').map(renderPlayerBattingStats)}
          
          <Text style={styles.dnbTitle}>Did Not Bat ({firstInnings.battingTeamName}):</Text>
          {(() => {
            const battingTeamPlayers = getTeamPlayers(firstInnings.battingTeamName);
            console.log(`DNB Check - Innings 1, Team: ${firstInnings.battingTeamName}, Players:`, JSON.stringify(battingTeamPlayers.map(p => ({ name: p.name, ballsFaced: p.battingStats.ballsFaced, status: p.battingStats.status }))));
            const dnbPlayers = battingTeamPlayers.filter(p => p.battingStats.ballsFaced === 0 && p.battingStats.status === 'not out');
            if (dnbPlayers.length === 0) return <Text style={styles.dnbPlayer}>- None -</Text>;
            return dnbPlayers.map(p => <Text key={p.id} style={styles.dnbPlayer}>{p.name}</Text>);
          })()}

          <View style={[styles.statsHeader, styles.marginTop]}>
            <Text style={styles.headerPlayerName}>Bowler</Text><Text style={styles.headerStatNarrow}>O</Text><Text style={styles.headerStatNarrow}>M</Text><Text style={styles.headerStatNarrow}>R</Text><Text style={styles.headerStatNarrow}>W</Text><Text style={styles.headerStatWide}>Econ</Text>
          </View>
          {getTeamPlayers(firstInnings.bowlingTeamName).filter(p => p.bowlingStats.ballsBowled > 0).map(renderPlayerBowlingStats)}
        </View>
      )}

      {secondInnings && (
        <View style={styles.inningsSection}>
          <Text style={styles.inningsTitle}>{secondInnings.battingTeamName} Innings: {secondInnings.score}/{secondInnings.wickets} ({secondInnings.oversCompleted}.{secondInnings.ballsInCurrentOver} Overs)</Text>
          <Text style={styles.targetText}>Target: {secondInnings.target}</Text>
          <View style={styles.statsHeader}>
            <Text style={styles.headerPlayerName}>Batsman</Text><Text style={styles.headerStatNarrow}>R</Text><Text style={styles.headerStatNarrow}>B</Text><Text style={styles.headerStatNarrow}>4s</Text><Text style={styles.headerStatNarrow}>6s</Text><Text style={styles.headerStatWide}>SR</Text>
          </View>
          {getTeamPlayers(secondInnings.battingTeamName).filter(p => p.battingStats.ballsFaced > 0 || p.battingStats.status === 'not out').map(renderPlayerBattingStats)}

          <Text style={styles.dnbTitle}>Did Not Bat ({secondInnings.battingTeamName}):</Text>
          {(() => {
            const battingTeamPlayers = getTeamPlayers(secondInnings.battingTeamName);
            console.log(`DNB Check - Innings 2, Team: ${secondInnings.battingTeamName}, Players:`, JSON.stringify(battingTeamPlayers.map(p => ({ name: p.name, ballsFaced: p.battingStats.ballsFaced, status: p.battingStats.status }))));
            const dnbPlayers = battingTeamPlayers.filter(p => p.battingStats.ballsFaced === 0 && p.battingStats.status === 'not out');
            if (dnbPlayers.length === 0) return <Text style={styles.dnbPlayer}>- None -</Text>;
            return dnbPlayers.map(p => <Text key={p.id} style={styles.dnbPlayer}>{p.name}</Text>);
          })()}

          <View style={[styles.statsHeader, styles.marginTop]}>
            <Text style={styles.headerPlayerName}>Bowler</Text><Text style={styles.headerStatNarrow}>O</Text><Text style={styles.headerStatNarrow}>M</Text><Text style={styles.headerStatNarrow}>R</Text><Text style={styles.headerStatNarrow}>W</Text><Text style={styles.headerStatWide}>Econ</Text>
          </View>
          {getTeamPlayers(secondInnings.bowlingTeamName).filter(p => p.bowlingStats.ballsBowled > 0).map(renderPlayerBowlingStats)}
        </View>
      )}
      <Button title="Go Home" onPress={() => navigation.navigate('Home')} />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, padding: 10 },
  loader: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  title: { fontSize: 24, fontWeight: 'bold', textAlign: 'center', marginBottom: 10 },
  matchConfigSection: {
    marginBottom: 15,
    padding: 10,
    backgroundColor: '#eef2f9', // Light blue-grey background
    borderRadius: 5,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#333',
  },
  configItem: {
    fontSize: 15,
    marginBottom: 4,
    color: '#454545',
  },
  matchResultText: { fontSize: 18, fontWeight: '500', textAlign: 'center', marginBottom: 20, color: 'green' },
  inningsSection: { marginBottom: 20, padding: 10, backgroundColor: '#f9f9f9', borderRadius: 5 },
  inningsTitle: { fontSize: 20, fontWeight: 'bold', marginBottom: 10 },
  targetText: { fontSize: 16, marginBottom: 10 }, 
  statsHeader: { 
    flexDirection: 'row', 
    justifyContent: 'space-between', 
    paddingBottom: 5, 
    borderBottomWidth: 1, 
    borderBottomColor: '#ccc', 
    marginBottom: 5,
    paddingHorizontal: 5, // Add some horizontal padding
  },
  headerPlayerName: { flex: 2.5, fontWeight: 'bold', textAlign: 'left' }, // Adjusted flex and alignment
  headerStatNarrow: { flex: 0.8, fontWeight: 'bold', textAlign: 'right' }, 
  headerStatWide: { flex: 1.2, fontWeight: 'bold', textAlign: 'right' }, 
  playerStatRow: { 
    flexDirection: 'row', 
    justifyContent: 'space-between', 
    paddingVertical: 4, // Increased padding
    paddingHorizontal: 5, // Add some horizontal padding
    borderBottomWidth: 1, // Add light separator for rows
    borderBottomColor: '#eee',
  },
  playerName: { flex: 2.5, textAlign: 'left' }, // Adjusted flex and alignment
  statValueNarrow: { flex: 0.8, textAlign: 'right' }, 
  statValueWide: { flex: 1.2, textAlign: 'right' }, 
  errorText: { fontSize: 18, textAlign: 'center', color: 'red', marginBottom: 10 },
  marginTop: { marginTop: 15 },
  dnbTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 15,
    marginBottom: 5,
    color: '#333',
  },
  dnbPlayer: {
    fontSize: 15,
    paddingLeft: 10,
    marginBottom: 3,
    color: '#555',
  },
}); 