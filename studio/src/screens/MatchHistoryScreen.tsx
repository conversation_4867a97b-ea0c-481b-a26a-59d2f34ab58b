import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, ActivityIndicator, Alert } from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { ScreenNavigationProp } from '../navigation/AppNavigator'; // Adjust path as needed
import { Match } from '../types/matchTypes';
import { loadAllMatches } from '../utils/storage';

// Mock data removed - this will eventually come from storage/API
// const MOCK_MATCHES: Match[] = [
//   { id: '1', teamA: 'Lions XI', teamB: 'Tigers XI', overs: 10, date: '2024-05-20', result: 'Lions XI won by 5 wickets' },
//   { id: '2', teamA: 'Panthers', teamB: 'Eagles', overs: 8, date: '2024-05-18', result: 'Eagles won by 20 runs' },
//   { id: '3', teamA: 'Warriors', teamB: 'Knights', overs: 12, date: '2024-05-15', result: 'Match Tied' },
// ];

export default function MatchHistoryScreen() {
  const navigation = useNavigation<ScreenNavigationProp<'MatchSummary'>>();
  const [completedMatches, setCompletedMatches] = useState<Match[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchMatches = async () => {
    setIsLoading(true);
    try {
      const allMatches = await loadAllMatches();
      const filteredMatches = allMatches.filter(match => match.status === 'completed');
      // Sort matches by creation date, newest first
      filteredMatches.sort((a, b) => (b.createdAt || 0) - (a.createdAt || 0));
      setCompletedMatches(filteredMatches);
    } catch (error) {
      console.error("Failed to load matches:", error);
      Alert.alert("Error", "Failed to load match history.");
      setCompletedMatches([]); // Clear matches on error
    }
    setIsLoading(false);
  };

  // useFocusEffect to refresh data when the screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      fetchMatches();
      return () => {}; // Optional cleanup function
    }, [])
  );

  const formatDate = (timestamp: number | undefined) => {
    if (!timestamp) return 'Date N/A';
    const date = new Date(timestamp);
    return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;
  };

  const renderMatchItem = ({ item }: { item: Match }) => (
    <TouchableOpacity 
      style={styles.matchItem}
      onPress={() => navigation.navigate('MatchSummary', { matchId: item.id })}
    >
      <Text style={styles.teamNames}>{`${item.teamAName} vs ${item.teamBName}`}</Text>
      <Text style={styles.matchDate}>{formatDate(item.createdAt)}</Text>
      <Text style={styles.matchResult}>{item.resultDescription || 'Result not available'}</Text>
    </TouchableOpacity>
  );

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#4f46e5" />
        <Text>Loading match history...</Text>
      </View>
    );
  }

  if (completedMatches.length === 0) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.emptyText}>No completed matches found.</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={completedMatches}
      renderItem={renderMatchItem}
      keyExtractor={(item) => item.id}
      style={styles.container}
      contentContainerStyle={completedMatches.length === 0 ? styles.centered : {}}
    />
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f3f4f6',
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  matchItem: {
    backgroundColor: 'white',
    padding: 15,
    marginVertical: 8,
    marginHorizontal: 16,
    borderRadius: 8,
    elevation: 2, // For Android shadow
    shadowColor: '#000', // For iOS shadow
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  teamNames: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 5,
  },
  matchDate: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 3,
  },
  matchResult: {
    fontSize: 15,
    color: '#374151',
    fontStyle: 'italic',
  },
  emptyText: {
    fontSize: 16,
    color: '#6b7280',
  },
});
