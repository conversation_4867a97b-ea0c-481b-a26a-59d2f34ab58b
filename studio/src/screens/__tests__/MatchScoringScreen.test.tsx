import React from 'react';
import { render, fireEvent, waitFor, screen, act, within } from '@testing-library/react-native';
import { <PERSON><PERSON>, AlertButton } from 'react-native';
import MatchScoringScreen from '../MatchScoringScreen';
import { Player, Match, PlayerBattingStats } from '../../types/matchTypes';

// Mock react-navigation
const mockNavigate = jest.fn();
const mockGoBack = jest.fn();
const mockSetOptions = jest.fn();

let mockCurrentRouteParams = { matchId: 'default-id' }; // Default, can be changed per test

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    goBack: mockGoBack,
    setOptions: mockSetOptions,
  }),
  useRoute: () => ({ // Returns the current state of mockCurrentRouteParams
    params: mockCurrentRouteParams,
  }),
}));

// Mock storage utilities (anticipating it will load and update match data)
jest.mock('../../utils/storage', () => ({
  loadMatch: jest.fn(),
  saveMatch: jest.fn(),
}));
const mockedLoadMatch = jest.mocked(require('../../utils/storage').loadMatch);
const mockedSaveMatch = jest.mocked(require('../../utils/storage').saveMatch);

const baseMatchData = {
  teamAName: 'Team Strikers',
  teamBName: 'Team Bowlers',
  oversPerInnings: 1,
  playersPerTeam: 2,
  lastManStanding: false,
  status: 'live_inning1', // Match is in first innings
  teamAPlayers: [
    { id: 'pA1', name: 'Striker A1', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
    { id: 'pA2', name: 'Striker A2', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
  ],
  teamBPlayers: [
    { id: 'pB1', name: 'Bowler B1', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
    { id: 'pB2', name: 'Bowler B2', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
  ],
  firstInnings: {
    battingTeamName: 'Team Strikers',
    bowlingTeamName: 'Team Bowlers',
    score: 0,
    wickets: 0,
    oversCompleted: 0,
    ballsInCurrentOver: 0,
    currentOverHistory: [],
    runsConcededThisOverMaiden:0,
    timeline: [],
    strikerId: undefined, // Explicitly for batsman selection
    nonStrikerId: undefined, // Explicitly for batsman selection
    currentBowlerId: undefined,
  },
  tossWinnerTeamName: 'Team Strikers',
  decision: 'Bat',
  createdAt: Date.now(),
  updatedAt: Date.now(),
};

describe('MatchScoringScreen', () => {
  let alertSpy: jest.SpyInstance;

  beforeEach(() => {
    mockNavigate.mockClear();
    mockGoBack.mockClear();
    mockSetOptions.mockClear();
    mockCurrentRouteParams = { matchId: 'default-id' }; // Reset to default for each test
    
    mockedLoadMatch.mockReset(); 
    mockedLoadMatch.mockImplementation(async (matchIdToLoad: string) => {
        if (matchIdToLoad === 'fail-this-id') { // This ID is now set via mockCurrentRouteParams
            return null;
        } else {
            const freshMatchData = JSON.parse(JSON.stringify({
                ...baseMatchData,
                id: matchIdToLoad,
                firstInnings: {
                    ...baseMatchData.firstInnings,
                    strikerId: undefined,
                    nonStrikerId: undefined,
                    currentBowlerId: undefined,
                }
            }));
            return freshMatchData;
        }
    });

    mockedSaveMatch.mockClear();
    mockedSaveMatch.mockResolvedValue(true); // Ensure saveMatch returns a truthy value by default

    alertSpy = jest.spyOn(Alert, 'alert').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders loading state initially then prompts for batsmen selection if not set', async () => {
    mockCurrentRouteParams = { matchId: 'test-match-id-123' }; // Set params for this test

    render(<MatchScoringScreen />);
    // await act(async () => {}); // Removed to prevent timeout

    expect(screen.getByText('Loading Scoreboard...')).toBeTruthy();

    await waitFor(() => {
      expect(screen.getByText('Select Opening Batsmen')).toBeTruthy();
      expect(screen.queryByText('Loading Scoreboard...')).toBeNull();
    }, { timeout: 3000 }); 
    
    expect(screen.getByText('Select Striker:')).toBeTruthy();
    expect(screen.getByText('Striker: Not selected')).toBeTruthy();
    expect(screen.getByText('Select Non-Striker:')).toBeTruthy();
    expect(screen.getByText('Non-Striker: Not selected')).toBeTruthy();
    // The "Confirm Batsmen" button should NOT be visible until both selections are made
    expect(screen.queryByText('Confirm Batsmen')).toBeNull(); 
    expect(alertSpy).not.toHaveBeenCalled();
  });

  it('shows error and goBack option if match fails to load', async () => {
    mockCurrentRouteParams = { matchId: 'fail-this-id' }; // Set params for this test to trigger failure in mockedLoadMatch
    
    // No need to override mockedLoadMatch here, as the beforeEach setup handles 'fail-this-id'

    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    render(<MatchScoringScreen />); 
    expect(screen.getByText('Loading Scoreboard...')).toBeTruthy();

    await waitFor(() => {
      expect(alertSpy).toHaveBeenCalledWith(
        "Error",
        "Failed to load match data.",
        expect.arrayContaining([
          expect.objectContaining({ text: "OK", onPress: expect.any(Function) })
        ])
      );
      expect(screen.queryByText('Loading Scoreboard...')).toBeNull();
    }, { timeout: 3000 });

    expect(consoleErrorSpy).not.toHaveBeenCalled();
    consoleErrorSpy.mockRestore();

    const alertCall = alertSpy.mock.calls[0];
    const okButton = alertCall[2]?.find((button: AlertButton) => button.text === 'OK');
    if (okButton?.onPress) {
      act(() => {
        okButton.onPress!();
      });
      expect(mockGoBack).toHaveBeenCalledTimes(1);
    }
  });

  it('allows selection of batsmen and bowler and starts scoring', async () => {
    mockCurrentRouteParams = { matchId: 'test-match-id-batsmen-bowler' };
    // Ensure loadMatch returns data where batsmen/bowler are not set
    // The default mockImplementation in beforeEach already does this (strikerId, etc. are undefined)

    render(<MatchScoringScreen />);

    // 1. Wait for batsmen selection UI
    await waitFor(() => {
      expect(screen.getByText('Select Opening Batsmen')).toBeTruthy();
      expect(screen.queryByText('Loading Scoreboard...')).toBeNull();
    }, { timeout: 3000 });

    // 2. Select Striker and Non-Striker
    // Use getAllByText for "Striker A1" as it appears for both striker and non-striker selection lists
    const strikerA1Buttons = screen.getAllByText('Striker A1');
    fireEvent.press(strikerA1Buttons[0]); // Assume first is for Striker selection

    // For "Striker A2", it also appears in both lists. Assume [1] is for non-striker after pA1 is selected as striker.
    const strikerA2Buttons = screen.getAllByText('Striker A2'); 
    fireEvent.press(strikerA2Buttons[1]); // Use index 1 for the non-striker selection button

    // 3. Confirm Batsmen
    const confirmBatsmenButton = screen.getByText('Confirm Batsmen');
    fireEvent.press(confirmBatsmenButton);

    // 4. Wait for bowler selection UI
    await waitFor(() => {
      expect(screen.getByText('Select Opening Bowler')).toBeTruthy();
      expect(screen.queryByText('Select Opening Batsmen')).toBeNull();
    }, { timeout: 3000 });

    // 5. Select Bowler
    const bowlerB1Button = screen.getByText('Bowler B1');
    fireEvent.press(bowlerB1Button);

    // 6. Confirm Bowler
    const confirmBowlerButton = screen.getByText('Confirm Bowler');
    fireEvent.press(confirmBowlerButton);

    // 7. Wait for main scoring interface
    await waitFor(() => {
      // Expect some elements unique to the scoring view
      expect(screen.getByText('Record Ball:')).toBeTruthy(); 
      expect(screen.queryByText('Select Opening Bowler')).toBeNull();
    }, { timeout: 3000 });

    // 8. Check saveMatch calls (optional, but good for verifying state persistence)
    // Expect saveMatch to have been called after confirming batsmen and after confirming bowler
    expect(mockedSaveMatch).toHaveBeenCalledTimes(2);
    const firstSaveCallArgs = mockedSaveMatch.mock.calls[0][0];
    expect(firstSaveCallArgs.firstInnings.strikerId).toBe(baseMatchData.teamAPlayers[0].id);
    expect(firstSaveCallArgs.firstInnings.nonStrikerId).toBe(baseMatchData.teamAPlayers[1].id);

    const secondSaveCallArgs = mockedSaveMatch.mock.calls[1][0];
    expect(secondSaveCallArgs.firstInnings.currentBowlerId).toBe(baseMatchData.teamBPlayers[0].id);
  });

  it('allows scoring runs and extras', async () => {
    mockCurrentRouteParams = { matchId: 'test-match-id-scoring-1' };
    // For this test, we need the match to be loaded with batsmen and bowler already selected.
    const matchDataWithPlayersSelected = JSON.parse(JSON.stringify({
        ...baseMatchData,
        id: 'test-match-id-scoring-1',
        firstInnings: {
            ...baseMatchData.firstInnings,
            strikerId: baseMatchData.teamAPlayers[0].id, // pA1
            nonStrikerId: baseMatchData.teamAPlayers[1].id, // pA2
            currentBowlerId: baseMatchData.teamBPlayers[0].id, // pB1
        }
    }));
    mockedLoadMatch.mockResolvedValueOnce(matchDataWithPlayersSelected);

    render(<MatchScoringScreen />);

    // 1. Wait for main scoring interface
    await waitFor(() => {
      expect(screen.getByText('Record Ball:')).toBeTruthy();
      expect(screen.getByText("Striker: Striker A1* (0/0)")).toBeTruthy();
      expect(screen.queryByText('Loading Scoreboard...')).toBeNull();
      expect(screen.queryByText('Select Opening Batsmen')).toBeNull();
      expect(screen.queryByText('Select Opening Bowler')).toBeNull();
    }, { timeout: 3000 });

    // Verify initial state for striker (pA1)
    expect(screen.getByTestId('striker-runs').props.children).toBe(0);
    expect(screen.getByTestId('striker-balls').props.children).toBe(0);

    // 2. Score 1 run
    const oneRunButton = screen.getByText('1');
    fireEvent.press(oneRunButton);

    // Wait for UI to update (score, striker runs, balls faced)
    await waitFor(() => {
        expect(screen.getByTestId('total-score').props.children.join('')).toBe('1/0');
        // After 1 run, pA1 scored, pA2 is the new striker.
        expect(screen.getByText(`Striker: ${baseMatchData.teamAPlayers[1].name}* (0/0)`)).toBeTruthy(); // pA2 is striker, with stats
        expect(screen.getByTestId('striker-runs').props.children).toBe(0); // pA2 (current striker) runs
        expect(screen.getByTestId('striker-balls').props.children).toBe(0); // pA2 (current striker) balls
        // Verify pA1 (now non-striker) stats. Non-striker name should have an asterisk based on current component logic.
        expect(screen.getByText(/Non-Striker: Striker A1\* \(1\/1\)/)).toBeTruthy();
    });
    expect(mockedSaveMatch).toHaveBeenCalledTimes(1);
    let lastSaveCallArgs = mockedSaveMatch.mock.calls[0][0];
    expect(lastSaveCallArgs.firstInnings.score).toBe(1);
    expect(lastSaveCallArgs.firstInnings.timeline.length).toBe(1);
    expect(lastSaveCallArgs.firstInnings.timeline[0]).toBe(1);

    // 3. Bowl a wide
    const wideButton = screen.getByText('WIDE');
    fireEvent.press(wideButton);

    await waitFor(() => {
        expect(screen.getByTestId('total-score').props.children.join('')).toBe('2/0'); // Joined array children
        // Striker remains pA2. Their individual runs/balls (0/0) should not change from just a wide.
        expect(screen.getByText(`Striker: ${baseMatchData.teamAPlayers[1].name}* (0/0)`)).toBeTruthy(); 
        expect(screen.getByTestId('striker-runs').props.children).toBe(0); // pA2's runs still 0
        expect(screen.getByTestId('striker-balls').props.children).toBe(0); // pA2's balls faced still 0
        // Non-striker pA1 also remains unchanged by the wide
        expect(screen.getByText(/Non-Striker: Striker A1\* \(1\/1\)/)).toBeTruthy();
    });
    expect(mockedSaveMatch).toHaveBeenCalledTimes(2);
    lastSaveCallArgs = mockedSaveMatch.mock.calls[1][0];
    expect(lastSaveCallArgs.firstInnings.score).toBe(2);
    expect(lastSaveCallArgs.firstInnings.timeline.length).toBe(2);
    expect(lastSaveCallArgs.firstInnings.timeline[1]).toBe('WIDE');
  });

  it('handles last wicket of 1st innings (2-player team) and prompts for 2nd innings batsmen', async () => {
    mockCurrentRouteParams = { matchId: 'test-match-id-last-wicket-1st-inns' };
    const matchDataForLastWicket = JSON.parse(JSON.stringify({
        ...baseMatchData, // 2 players per team, non-LMS. So 1 wicket ends innings.
        id: 'test-match-id-last-wicket-1st-inns',
        oversPerInnings: 1, // Keep it short
        firstInnings: {
            ...baseMatchData.firstInnings,
            score: 0, // Start score at 0 for clarity on target
            strikerId: baseMatchData.teamAPlayers[0].id, // pA1
            nonStrikerId: baseMatchData.teamAPlayers[1].id, // pA2
            currentBowlerId: baseMatchData.teamBPlayers[0].id, // pB1
            wickets: 0, // Start with 0 wickets
        },
        // Ensure no secondInnings initially
        secondInnings: undefined, 
    }));
    // Remove secondInnings if baseMatchData might have it by default from other tests
    delete matchDataForLastWicket.secondInnings; 

    mockedLoadMatch.mockResolvedValueOnce(matchDataForLastWicket);

    render(<MatchScoringScreen />);

    // 1. Wait for main scoring interface
    await waitFor(() => {
      expect(screen.getByText(`Striker: ${baseMatchData.teamAPlayers[0].name}* (0/0)`)).toBeTruthy();
      expect(screen.queryByText('Loading Scoreboard...')).toBeNull();
    });

    // 2. Record a Wicket (this should be the only wicket needed to end the 1st innings)
    const wicketButton = screen.getByText('Wicket');
    fireEvent.press(wicketButton);

    // 3. Verify UI transitions to select batsmen for 2nd innings
    await waitFor(() => {
      expect(screen.getByText('Select Opening Batsmen')).toBeTruthy(); 
    });

    // 4. Verify match state updates (via saveMatch mock)
    expect(mockedSaveMatch).toHaveBeenCalledTimes(1);
    const savedMatch: Match = mockedSaveMatch.mock.calls[0][0];
    
    expect(savedMatch.status).toBe('live_inning2');
    expect(savedMatch.firstInnings!.wickets).toBe(1);
    expect(savedMatch.firstInnings!.timeline[savedMatch.firstInnings!.timeline.length - 1]).toBe('WICKET');
    expect(savedMatch.secondInnings).toBeDefined();
    expect(savedMatch.secondInnings!.target).toBe(1); // Score was 0, so target is 0 + 1 = 1
    expect(savedMatch.secondInnings!.battingTeamName).toBe(baseMatchData.teamBName); // Team B bats second

    // Verify pA1 (who got out) status
    const playerA1InSavedMatch = savedMatch.teamAPlayers.find((p: Player) => p.id === baseMatchData.teamAPlayers[0].id);
    expect(playerA1InSavedMatch!.battingStats.status).toBe('out');

    // 5. Verify UI elements for 2nd innings batsmen selection
    expect(screen.getByText(`Team: ${baseMatchData.teamBName}`)).toBeTruthy(); // Check correct team is displayed
    expect(screen.getByText('Select Striker:')).toBeTruthy();
    expect(screen.getByText('Select Non-Striker:')).toBeTruthy();
    
    // Check that player names from Team B are present as selectable options (likely appearing twice)
    const bowlerB1Elements = screen.getAllByText(baseMatchData.teamBPlayers[0].name); // Bowler B1
    expect(bowlerB1Elements.length).toBe(2); // Expecting button in Striker list and Non-Striker list
    const bowlerB2Elements = screen.getAllByText(baseMatchData.teamBPlayers[1].name); // Bowler B2
    expect(bowlerB2Elements.length).toBe(2); // Expecting button in Striker list and Non-Striker list
  });

  it('handles over completion and prompts for new bowler selection', async () => {
    mockCurrentRouteParams = { matchId: 'test-match-id-over-complete' };
    const matchDataForOverCompletion = JSON.parse(JSON.stringify({
      ...baseMatchData,
      id: 'test-match-id-over-complete',
      oversPerInnings: 2, // 2-over match to allow bowler change after 1st over
      teamAPlayers: [ // Need at least 2 bowlers in Team B if we want to select a *different* new bowler
        { id: 'pA1', name: 'Batsman A1', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
        { id: 'pA2', name: 'Batsman A2', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
      ],
      teamBPlayers: [
        { id: 'pB1', name: 'Bowler B1', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
        { id: 'pB2', name: 'Bowler B2', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
      ],
      firstInnings: {
        ...baseMatchData.firstInnings,
        battingTeamName: 'Team Strikers', // Ensure correct team names from baseMatchData if overridden
        bowlingTeamName: 'Team Bowlers',
        strikerId: 'pA1', // Batsman A1 from this test's teamAPlayers
        nonStrikerId: 'pA2', // Batsman A2
        currentBowlerId: 'pB1', // Bowler B1
        oversCompleted: 0,
        ballsInCurrentOver: 0,
        currentOverHistory: [],
      },
      secondInnings: undefined, // Ensure no second innings data for this test setup
    }));
    delete matchDataForOverCompletion.secondInnings;

    mockedLoadMatch.mockResolvedValueOnce(matchDataForOverCompletion);
    // Alert spy is in beforeEach, mockedSaveMatch resolves true by default

    render(<MatchScoringScreen />);

    // 1. Wait for main scoring interface
    await waitFor(() => {
      expect(screen.getByText('Striker: Batsman A1* (0/0)')).toBeTruthy();
      expect(screen.queryByText('Loading Scoreboard...')).toBeNull();
    });

    // 2. Bowl 5 legal deliveries (e.g., 5 dots)
    // Try to find the '0' button using getByRole with its accessible name
    const dotButton = screen.getByRole('button', { name: '0' });

    for (let i = 0; i < 5; i++) {
      fireEvent.press(dotButton); 
      // Minimal wait for saveMatch to be processed, avoid slowing down too much
      await waitFor(() => expect(mockedSaveMatch).toHaveBeenCalledTimes(i + 1));
    }
    
    // Verify overs: 0.5
    await waitFor(() => expect(screen.getByText('0.5')).toBeTruthy());

    // 3. Bowl the 6th legal delivery (completing the over)
    // Let's make it a single to test strike rotation as well
    const oneRunButton = screen.getByText('1');
    fireEvent.press(oneRunButton);

    // 4. Verify "Over Complete!" alert and UI transition
    await waitFor(() => {
      expect(alertSpy).toHaveBeenCalledWith(
        "Over Complete!",
        expect.stringContaining('Over 1 finished by Bowler B1')
      );
      expect(screen.getByText('Select Bowler for Next Over')).toBeTruthy();
    });

    // 5. Verify match state from saveMatch call for the 6th ball
    expect(mockedSaveMatch).toHaveBeenCalledTimes(6);
    const finalSaveCallArgs = mockedSaveMatch.mock.calls[5][0]; // 6th call (0-indexed)
    expect(finalSaveCallArgs.firstInnings.oversCompleted).toBe(1);
    expect(finalSaveCallArgs.firstInnings.ballsInCurrentOver).toBe(0);
    // currentOverHistory for the *new* over (bowled by new bowler) should be empty or reset in component
    // The component logic clears currentOverHistory in handleSelectNewBowler -> updateAndSaveMatch.
    // So, finalSaveCallArgs (from the 6th ball) will still have the old over history.
    // The state *after* selecting new bowler would have it cleared.
    // For now, let's check the score and strike rotation.
    expect(finalSaveCallArgs.firstInnings.score).toBe(1); // 5 dots + 1 run
    expect(finalSaveCallArgs.firstInnings.strikerId).toBe('pA2'); // pA1 faced 6th ball (single), pA2 on strike for next over
    expect(finalSaveCallArgs.firstInnings.nonStrikerId).toBe('pA1');

    // 6. Verify UI for selecting new bowler
    expect(screen.getByText(`Team: ${matchDataForOverCompletion.firstInnings.bowlingTeamName}`)).toBeTruthy();
    // Bowler B1 (who just bowled) and Bowler B2 should be options
    expect(screen.getByText('Bowler B1 (Current)')).toBeTruthy();
    expect(screen.getByText('Bowler B2')).toBeTruthy(); 
    // (Button to confirm bowler would also be there if one is selected)
  });

  it('handles target achieved in 2nd innings and over completion', async () => {
    mockCurrentRouteParams = { matchId: 'test-match-id-target-achieved' };
    const firstInningsScore = 0;
    const targetToWin = firstInningsScore + 1; // Team B needs 1 run to win

    // Use a more self-contained match data setup for this specific test
    const matchDataForTargetAchieved: Match = JSON.parse(JSON.stringify({
      id: 'test-match-id-target-achieved',
      teamAName: 'Team Strikers', // Bowling team in 2nd innings context
      teamBName: 'Team Bowlers',  // Batting team in 2nd innings context
      oversPerInnings: 1,
      playersPerTeam: 2,
      tossWonBy: 'Team Strikers', // Team A
      choseTo: 'Bat',
      teamAPlayers: [ // These will be BOWLING in the 2nd innings
        { id: 'pA1', name: 'Bowler A1', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
        { id: 'pA2', name: 'Bowler A2', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
      ],
      teamBPlayers: [ // These will be BATTING in the 2nd innings
        { id: 'pB1', name: 'Striker B1', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
        { id: 'pB2', name: 'Striker B2', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
      ],
      firstInnings: {
        battingTeamName: 'Team Strikers', // Team A
        bowlingTeamName: 'Team Bowlers',  // Team B
        score: firstInningsScore, // Team A scored 0
        wickets: 2, // Max wickets for 2 players
        oversCompleted: 1,
        ballsInCurrentOver: 0,
        currentOverHistory: ['W','W'], // Example, doesn't strictly matter for 2nd inns test
        timeline: ['W','W'],
        strikerId: 'someTeamAPlayer1', // Placeholder
        nonStrikerId: 'someTeamAPlayer2', // Placeholder
        currentBowlerId: 'pB1', // Placeholder
      },
      secondInnings: {
        battingTeamName: 'Team Bowlers', // Team B is batting
        bowlingTeamName: 'Team Strikers', // Team A is bowling
        score: 0, // Team B starts at 0
        wickets: 0,
        oversCompleted: 0,
        ballsInCurrentOver: 0,
        currentOverHistory: [],
        timeline: [],
        runsConcededThisOverMaiden: 0,
        target: targetToWin, // Team B needs 1 run to win
        strikerId: 'pB1', // Striker from Team B
        nonStrikerId: 'pB2', // Non-striker from Team B
        currentBowlerId: 'pA1', // Bowler from Team A
      },
      status: 'live_inning2', // Start in 2nd innings
      venue: 'Test Venue Target',
      matchDate: new Date().toISOString(),
      lastBallPlayed: null,
      matchResult: '',
      matchWinnerTeamName: null,
    }));

    mockedLoadMatch.mockResolvedValueOnce(matchDataForTargetAchieved);

    render(<MatchScoringScreen />);

    // 1. Wait for main scoring interface (2nd innings)
    await waitFor(() => {
      expect(screen.getByText('Striker: Striker B1* (0/0)')).toBeTruthy();
      expect(screen.getByText('Non-Striker: Striker B2* (0/0)')).toBeTruthy();
      // Bowler A1 is from teamAPlayers, their name is 'Bowler A1'
      expect(screen.getByText(/Bowler: Bowler A1/)).toBeTruthy();
      expect(screen.queryByText('Loading Scoreboard...')).toBeNull();
      expect(screen.queryByText('Select Opening Batsmen')).toBeNull(); // Should not be in selection mode
    });

    // 2. Bowl 5 legal deliveries (dots)
    const dotButton = screen.getByRole('button', { name: '0' });
    for (let i = 0; i < 5; i++) {
      fireEvent.press(dotButton);
      await waitFor(() => expect(mockedSaveMatch).toHaveBeenCalledTimes(i + 1));
    }

    // Verify score and overs after 5 balls
    await waitFor(() => {
      // Team B (secondInnings) score should be 0/0, overs 0.5
      expect(screen.getByTestId('total-score').props.children.join('')).toBe('0/0');
      expect(screen.getByText('0.5')).toBeTruthy(); // Overs display
    });

    // 3. Bowl the 6th legal delivery (1 run - achieves target and completes over)
    const oneRunButton = screen.getByText('1');
    fireEvent.press(oneRunButton);

    // 4. Verify "Match Over!" alert
    await waitFor(() => {
      expect(alertSpy).toHaveBeenCalledWith(
        "Match Over!",
        expect.stringContaining(`${matchDataForTargetAchieved.secondInnings!.battingTeamName} won by ${matchDataForTargetAchieved.playersPerTeam - 0} wickets`)
      );
      // After match is over, it should not prompt for a new bowler.
      // If there's a specific match summary text, we can check for that.
      // For now, ensure the bowler selection UI is gone.
      expect(screen.queryByText('Select Bowler for Next Over')).toBeNull();
    });

    // 5. Verify match state from the final saveMatch call (6th ball)
    expect(mockedSaveMatch).toHaveBeenCalledTimes(6);
    const finalSaveCallArgs: Match = mockedSaveMatch.mock.calls[5][0];

    expect(finalSaveCallArgs.status).toBe('completed');
    expect(finalSaveCallArgs.matchWinnerTeamName).toBe(matchDataForTargetAchieved.secondInnings!.battingTeamName);
    expect(finalSaveCallArgs.resultDescription).toContain(`${matchDataForTargetAchieved.secondInnings!.battingTeamName} won by`); // Standard win message format

    expect(finalSaveCallArgs.secondInnings!.score).toBe(targetToWin); // Team B scored 1 run
    expect(finalSaveCallArgs.secondInnings!.oversCompleted).toBe(1); // Over was completed
    expect(finalSaveCallArgs.secondInnings!.ballsInCurrentOver).toBe(0);
    
    // After 1 run, pB1 (Striker B1) scored it, pB2 (Striker B2) should be at striker's end for the (notional) next ball
    expect(finalSaveCallArgs.secondInnings!.strikerId).toBe('pB2');
    expect(finalSaveCallArgs.secondInnings!.nonStrikerId).toBe('pB1');

    // Verify player stats for Striker B1 (pB1)
    const playerB1FromSave = finalSaveCallArgs.teamBPlayers.find(p => p.id === 'pB1');
    expect(playerB1FromSave!.battingStats.runsScored).toBe(1);
    expect(playerB1FromSave!.battingStats.ballsFaced).toBe(6); // Faced all 6 balls in the over

    // Verify player stats for Striker B2 (pB2) - did not face a ball
    const playerB2FromSave = finalSaveCallArgs.teamBPlayers.find(p => p.id === 'pB2');
    expect(playerB2FromSave!.battingStats.runsScored).toBe(0);
    expect(playerB2FromSave!.battingStats.ballsFaced).toBe(0);

    // Verify player stats for Bowler A1 (pA1)
    const playerA1FromSave = finalSaveCallArgs.teamAPlayers.find(p => p.id === 'pA1');
    expect(playerA1FromSave!.bowlingStats.ballsBowled).toBe(6); // Bowled 6 balls
    expect(playerA1FromSave!.bowlingStats.runsConceded).toBe(1); // Conceded 1 run
  });

  it('handles match completion when target is NOT achieved (all out in 2nd innings)', async () => {
    mockCurrentRouteParams = { matchId: 'test-match-id-bowling-team-wins' };
    const firstInningsScoreByTeamX = 10;
    const targetForTeamY = firstInningsScoreByTeamX + 1;

    const matchDataBowlingTeamWins: Match = JSON.parse(JSON.stringify({
      id: 'test-match-id-bowling-team-wins',
      teamAName: 'Team X', // Will be bowling in 2nd innings
      teamBName: 'Team Y', // Will be batting in 2nd innings
      oversPerInnings: 2, // More than enough
      playersPerTeam: 2,
      lastManStanding: false, // So, 1 wicket in 2nd inns = all out
      tossWonBy: 'Team X',
      choseTo: 'Bat',
      teamAPlayers: [ // Team X players
        { id: 'pX1', name: 'Player X1', battingStats: { runsScored: 5, ballsFaced: 5, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
        { id: 'pX2', name: 'Player X2', battingStats: { runsScored: 5, ballsFaced: 5, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
      ],
      teamBPlayers: [ // Team Y players
        { id: 'pY1', name: 'Player Y1', battingStats: { runsScored: 0, ballsFaced: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
        { id: 'pY2', name: 'Player Y2', battingStats: { runsScored: 0, ballsFaced: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
      ],
      firstInnings: {
        battingTeamName: 'Team X',
        bowlingTeamName: 'Team Y',
        score: firstInningsScoreByTeamX, // Team X scored 10
        wickets: 0, // Example
        oversCompleted: 1,
        ballsInCurrentOver: 0,
        currentOverHistory: Array(6).fill('1'), // Example
        timeline: Array(6).fill(1),
        strikerId: 'pX1',
        nonStrikerId: 'pX2',
        currentBowlerId: 'pY1',
      },
      secondInnings: {
        battingTeamName: 'Team Y',
        bowlingTeamName: 'Team X',
        score: 0, // Team Y starts at 0
        wickets: 0, // Team Y starts with 0 wickets down
        oversCompleted: 0,
        ballsInCurrentOver: 0,
        currentOverHistory: [],
        timeline: [],
        runsConcededThisOverMaiden: 0,
        target: targetForTeamY, // Team Y needs 11 to win
        strikerId: 'pY1', // Player Y1 batting
        nonStrikerId: 'pY2', // Player Y2 non-striker
        currentBowlerId: 'pX1', // Player X1 bowling
      },
      status: 'live_inning2',
      venue: 'Test Venue Bowling Wins',
      matchDate: new Date().toISOString(),
      lastBallPlayed: null,
      matchResult: '',
      matchWinnerTeamName: null,
    }));

    mockedLoadMatch.mockResolvedValueOnce(matchDataBowlingTeamWins);
    render(<MatchScoringScreen />);

    // 1. Wait for 2nd innings scoring interface
    await waitFor(() => {
      expect(screen.getByText('Striker: Player Y1* (0/0)')).toBeTruthy();
      expect(screen.getByText('Non-Striker: Player Y2* (0/0)')).toBeTruthy();
      expect(screen.getByText(/Bowler: Player X1/)).toBeTruthy();
      expect(screen.queryByText('Loading Scoreboard...')).toBeNull();
    });

    // 2. Record a Wicket (this should be the only wicket needed for Team Y to be all out)
    const wicketButton = screen.getByText('Wicket');
    fireEvent.press(wicketButton);

    // 3. Verify "Match Over!" alert for bowling team (Team X) winning
    await waitFor(() => {
      const runsToWinBy = targetForTeamY - 1 - matchDataBowlingTeamWins.secondInnings!.score; // Target was 11, score 0 => win by 10 runs
      expect(alertSpy).toHaveBeenCalledWith(
        "Match Over!",
        expect.stringContaining(`${matchDataBowlingTeamWins.firstInnings!.battingTeamName} won by ${runsToWinBy} runs`)
      );
      expect(screen.queryByText('Select Bowler for Next Over')).toBeNull();
    });

    // 4. Verify match state from the final saveMatch call
    expect(mockedSaveMatch).toHaveBeenCalledTimes(1);
    const finalSaveCallArgs: Match = mockedSaveMatch.mock.calls[0][0];

    expect(finalSaveCallArgs.status).toBe('completed');
    expect(finalSaveCallArgs.matchWinnerTeamName).toBe(matchDataBowlingTeamWins.firstInnings!.battingTeamName); // Team X (bowling team in 2nd inns)
    expect(finalSaveCallArgs.resultDescription).toContain(`${matchDataBowlingTeamWins.firstInnings!.battingTeamName} won by`);

    expect(finalSaveCallArgs.secondInnings!.score).toBe(0); // Team Y scored 0 before wicket
    expect(finalSaveCallArgs.secondInnings!.wickets).toBe(1); // Team Y lost 1 wicket (all out for 2 players, no LMS)
    
    // Verify player stats: pY1 got out, pX1 took the wicket
    const playerY1FromSave = finalSaveCallArgs.teamBPlayers.find(p => p.id === 'pY1'); // Team Y is teamBPlayers in MatchData
    expect(playerY1FromSave!.battingStats.status).toBe('out');
    expect(playerY1FromSave!.battingStats.ballsFaced).toBe(1); // Assumes wicket is 1 ball faced

    const playerX1FromSave = finalSaveCallArgs.teamAPlayers.find(p => p.id === 'pX1'); // Team X is teamAPlayers in MatchData
    expect(playerX1FromSave!.bowlingStats.wicketsTaken).toBe(1);
    expect(playerX1FromSave!.bowlingStats.ballsBowled).toBe(1); // Assumes wicket is 1 ball bowled by pX1
  });

  it('handles match completion when target is NOT achieved (overs run out in 2nd innings)', async () => {
    mockCurrentRouteParams = { matchId: 'test-match-id-overs-run-out' };
    const firstInningsScoreByTeamX = 20;
    const targetForTeamY = firstInningsScoreByTeamX + 1;

    const matchDataOversRunOut: Match = JSON.parse(JSON.stringify({
      id: 'test-match-id-overs-run-out',
      teamAName: 'Team X', // Batting 1st, Bowling 2nd
      teamBName: 'Team Y', // Batting 2nd
      oversPerInnings: 1, // Crucial: 1-over match
      playersPerTeam: 2,
      lastManStanding: false,
      tossWonBy: 'Team X',
      choseTo: 'Bat',
      teamAPlayers: [ // Team X players
        { id: 'pX1', name: 'Player X1', battingStats: { runsScored: 10, ballsFaced: 6, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
        { id: 'pX2', name: 'Player X2', battingStats: { runsScored: 10, ballsFaced: 6, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
      ],
      teamBPlayers: [ // Team Y players
        { id: 'pY1', name: 'Player Y1', battingStats: { runsScored: 0, ballsFaced: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
        { id: 'pY2', name: 'Player Y2', battingStats: { runsScored: 0, ballsFaced: 0, status: 'not out' }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
      ],
      firstInnings: {
        battingTeamName: 'Team X',
        bowlingTeamName: 'Team Y',
        score: firstInningsScoreByTeamX,
        wickets: 0,
        oversCompleted: 1,
        ballsInCurrentOver: 0,
        currentOverHistory: Array(6).fill('3'), // Example: Team X scored 20 (adjust if needed)
        timeline: Array(6).fill(3),
        strikerId: 'pX1',
        nonStrikerId: 'pX2',
        currentBowlerId: 'pY1',
      },
      secondInnings: {
        battingTeamName: 'Team Y',
        bowlingTeamName: 'Team X',
        score: 0, // Team Y starts at 0
        wickets: 0,
        oversCompleted: 0,
        ballsInCurrentOver: 0,
        currentOverHistory: [],
        timeline: [],
        runsConcededThisOverMaiden: 0,
        target: targetForTeamY, // Team Y needs 21 to win
        strikerId: 'pY1', // Player Y1 batting
        nonStrikerId: 'pY2', // Player Y2 non-striker
        currentBowlerId: 'pX1', // Player X1 bowling
      },
      status: 'live_inning2',
      venue: 'Test Venue Overs Run Out',
      matchDate: new Date().toISOString(),
      lastBallPlayed: null,
      matchResult: '',
      matchWinnerTeamName: null,
    }));

    mockedLoadMatch.mockResolvedValueOnce(matchDataOversRunOut);
    render(<MatchScoringScreen />);

    // 1. Wait for 2nd innings scoring interface
    await waitFor(() => {
      expect(screen.getByText('Striker: Player Y1* (0/0)')).toBeTruthy();
      expect(screen.getByText('Non-Striker: Player Y2* (0/0)')).toBeTruthy();
      expect(screen.getByText(/Bowler: Player X1/)).toBeTruthy();
      expect(screen.queryByText('Loading Scoreboard...')).toBeNull();
    });

    // 2. Bowl 5 dot balls (score 0 from 5 balls)
    const dotButton = screen.getByRole('button', { name: '0' });
    for (let i = 0; i < 5; i++) {
      fireEvent.press(dotButton);
      await waitFor(() => expect(mockedSaveMatch).toHaveBeenCalledTimes(i + 1));
    }
    
    // Team Y score 0/0, overs 0.5
    await waitFor(() => {
        expect(screen.getByTestId('total-score').props.children.join('')).toBe('0/0');
        expect(screen.getByText('0.5')).toBeTruthy(); 
    });

    // 3. Bowl the 6th ball as 1 run. Team Y scores 1 run in total. Overs complete.
    const oneRunButton = screen.getByText('1');
    fireEvent.press(oneRunButton);
    const teamYFinalScore = 1;

    // 4. Verify "Match Over!" alert for Team X winning by runs
    await waitFor(() => {
      const runsTeamXWonBy = firstInningsScoreByTeamX - teamYFinalScore; // 20 - 1 = 19 runs
      expect(alertSpy).toHaveBeenCalledWith(
        "Match Over!",
        expect.stringContaining(`${matchDataOversRunOut.firstInnings!.battingTeamName} won by ${runsTeamXWonBy} runs`)
      );
      expect(screen.queryByText('Select Bowler for Next Over')).toBeNull();
    });

    // 5. Verify match state from the final saveMatch call
    expect(mockedSaveMatch).toHaveBeenCalledTimes(6); // 5 dots + 1 run
    const finalSaveCallArgs: Match = mockedSaveMatch.mock.calls[5][0];

    expect(finalSaveCallArgs.status).toBe('completed');
    expect(finalSaveCallArgs.matchWinnerTeamName).toBe(matchDataOversRunOut.firstInnings!.battingTeamName); // Team X
    expect(finalSaveCallArgs.resultDescription).toContain(`${matchDataOversRunOut.firstInnings!.battingTeamName} won by`);

    expect(finalSaveCallArgs.secondInnings!.score).toBe(teamYFinalScore);
    expect(finalSaveCallArgs.secondInnings!.oversCompleted).toBe(1);
    expect(finalSaveCallArgs.secondInnings!.ballsInCurrentOver).toBe(0);
    
    // Verify player stats
    // Player Y1 faced all 6 balls: 5 dots, then 1 run on the 6th ball.
    const playerY1_final = finalSaveCallArgs.teamBPlayers.find(p => p.id === 'pY1');
    expect(playerY1_final!.battingStats.runsScored).toBe(1);
    expect(playerY1_final!.battingStats.ballsFaced).toBe(6);

    // Player Y2 did not face any balls.
    const playerY2_final = finalSaveCallArgs.teamBPlayers.find(p => p.id === 'pY2');
    expect(playerY2_final!.battingStats.runsScored).toBe(0);
    expect(playerY2_final!.battingStats.ballsFaced).toBe(0);

    const playerX1_bowler_final = finalSaveCallArgs.teamAPlayers.find(p => p.id === 'pX1');
    expect(playerX1_bowler_final!.bowlingStats.ballsBowled).toBe(6);
    expect(playerX1_bowler_final!.bowlingStats.runsConceded).toBe(teamYFinalScore);
  });

  it('handles a no-ball, subsequent free hit, and checks score and player stats', async () => {
    mockCurrentRouteParams = { matchId: 'test-match-id-noball-freehit' };
    const matchDataForNoBall: Match = JSON.parse(JSON.stringify({
      ...baseMatchData,
      id: 'test-match-id-noball-freehit',
      oversPerInnings: 1,
      firstInnings: {
        ...baseMatchData.firstInnings,
        strikerId: baseMatchData.teamAPlayers[0].id, // pA1
        nonStrikerId: baseMatchData.teamAPlayers[1].id, // pA2
        currentBowlerId: baseMatchData.teamBPlayers[0].id, // pB1
        score: 0,
        wickets: 0,
        oversCompleted: 0,
        ballsInCurrentOver: 0,
        currentOverHistory: [],
        timeline: [],
        runsConcededThisOverMaiden: 0, // Ensure all required fields are present
      }
    }));

    matchDataForNoBall.teamAPlayers = JSON.parse(JSON.stringify(baseMatchData.teamAPlayers)).map((p: Player) => ({
      ...p,
      battingStats: { ...p.battingStats, runsScored: 0, ballsFaced: 0, status: 'not out' as PlayerBattingStats['status'] },
      bowlingStats: { ...p.bowlingStats } 
    }));
    matchDataForNoBall.teamBPlayers = JSON.parse(JSON.stringify(baseMatchData.teamBPlayers)).map((p: Player) => ({
      ...p,
      battingStats: { ...p.battingStats, status: 'not out' as PlayerBattingStats['status'] }, 
      bowlingStats: { ...p.bowlingStats, ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, noBallsBowled: 0 }
    }));

    mockedLoadMatch.mockResolvedValueOnce(matchDataForNoBall);

    render(<MatchScoringScreen />);

    // 1. Wait for main scoring interface
    await waitFor(() => {
      expect(screen.getByText(`Striker: ${baseMatchData.teamAPlayers[0].name}* (0/0)`)).toBeTruthy();
      expect(screen.queryByText('Loading Scoreboard...')).toBeNull();
    });

    // 2. Bowl a No Ball (assuming a button "NO BALL")
    const noBallButton = screen.getByText('NO_BALL'); 
    fireEvent.press(noBallButton);

    // 3. Verify UI updates after No Ball
    await waitFor(() => {
      expect(screen.getByTestId('total-score').props.children.join('')).toBe('1/0');
      expect(screen.getByText(`Striker: ${baseMatchData.teamAPlayers[0].name}* (0/0)`)).toBeTruthy();
    });

    expect(mockedSaveMatch).toHaveBeenCalledTimes(1);
    let lastSaveCallArgs: Match = mockedSaveMatch.mock.calls[0][0];
    expect(lastSaveCallArgs.firstInnings!.score).toBe(1);
    expect(lastSaveCallArgs.firstInnings!.timeline).toEqual(['NO_BALL']);

    const bowlerPAfterNB = lastSaveCallArgs.teamBPlayers.find(p => p.id === baseMatchData.teamBPlayers[0].id);
    expect(bowlerPAfterNB!.bowlingStats.runsConceded).toBe(1);
    expect(bowlerPAfterNB!.bowlingStats.noBallsBowled).toBe(1);
    expect(bowlerPAfterNB!.bowlingStats.ballsBowled).toBe(0); 

    const strikerPAfterNB = lastSaveCallArgs.teamAPlayers.find(p => p.id === baseMatchData.teamAPlayers[0].id);
    expect(strikerPAfterNB!.battingStats.runsScored).toBe(0);
    expect(strikerPAfterNB!.battingStats.ballsFaced).toBe(0);

    // 4. Score 4 runs on the Free Hit delivery
    const fourRunsButton = screen.getByText('4');
    fireEvent.press(fourRunsButton);

    // 5. Verify UI and state updates after Free Hit
    await waitFor(() => {
      expect(screen.getByTestId('total-score').props.children.join('')).toBe('5/0');
      expect(screen.getByText(`Striker: ${baseMatchData.teamAPlayers[0].name}* (4/1)`)).toBeTruthy();
    });

    expect(mockedSaveMatch).toHaveBeenCalledTimes(2);
    lastSaveCallArgs = mockedSaveMatch.mock.calls[1][0];
    expect(lastSaveCallArgs.firstInnings!.score).toBe(5);
    expect(lastSaveCallArgs.firstInnings!.timeline).toEqual(['NO_BALL', 4]);
    expect(lastSaveCallArgs.firstInnings!.ballsInCurrentOver).toBe(1); 
    expect(lastSaveCallArgs.firstInnings!.oversCompleted).toBe(0); 

    const bowlerPAfterFreeHit = lastSaveCallArgs.teamBPlayers.find(p => p.id === baseMatchData.teamBPlayers[0].id);
    expect(bowlerPAfterFreeHit!.bowlingStats.runsConceded).toBe(5); 
    expect(bowlerPAfterFreeHit!.bowlingStats.noBallsBowled).toBe(1);
    expect(bowlerPAfterFreeHit!.bowlingStats.ballsBowled).toBe(1); 

    const strikerPAfterFreeHit = lastSaveCallArgs.teamAPlayers.find(p => p.id === baseMatchData.teamAPlayers[0].id);
    expect(strikerPAfterFreeHit!.battingStats.runsScored).toBe(4);
    expect(strikerPAfterFreeHit!.battingStats.ballsFaced).toBe(1); 
  });

  it('handles a wicket on a no-ball (free hit) correctly', async () => {
    mockCurrentRouteParams = { matchId: 'test-match-id-wicket-noball' };
    const matchDataWicketOnNoBall: Match = JSON.parse(JSON.stringify({
      ...baseMatchData,
      id: 'test-match-id-wicket-noball',
      oversPerInnings: 1,
      firstInnings: {
        ...baseMatchData.firstInnings,
        strikerId: baseMatchData.teamAPlayers[0].id, // pA1
        nonStrikerId: baseMatchData.teamAPlayers[1].id, // pA2
        currentBowlerId: baseMatchData.teamBPlayers[0].id, // pB1
        score: 0,
        wickets: 0,
        oversCompleted: 0,
        ballsInCurrentOver: 0,
        currentOverHistory: [],
        timeline: [],
        runsConcededThisOverMaiden: 0,
      }
    }));

    matchDataWicketOnNoBall.teamAPlayers = JSON.parse(JSON.stringify(baseMatchData.teamAPlayers)).map((p: Player) => ({
      ...p,
      battingStats: { ...p.battingStats, runsScored: 0, ballsFaced: 0, status: 'not out' as PlayerBattingStats['status'] },
      bowlingStats: { ...p.bowlingStats }
    }));
    matchDataWicketOnNoBall.teamBPlayers = JSON.parse(JSON.stringify(baseMatchData.teamBPlayers)).map((p: Player) => ({
      ...p,
      battingStats: { ...p.battingStats, status: 'not out' as PlayerBattingStats['status'] },
      bowlingStats: { ...p.bowlingStats, ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, noBallsBowled: 0 }
    }));

    mockedLoadMatch.mockResolvedValueOnce(matchDataWicketOnNoBall);

    render(<MatchScoringScreen />);

    // 1. Wait for main scoring interface
    await waitFor(() => {
      expect(screen.getByText(`Striker: ${baseMatchData.teamAPlayers[0].name}* (0/0)`)).toBeTruthy();
    });

    // 2. Bowl a No Ball
    const noBallButton = screen.getByText('NO_BALL');
    fireEvent.press(noBallButton);

    // 3. Verify state after No Ball
    await waitFor(() => {
      expect(screen.getByTestId('total-score').props.children.join('')).toBe('1/0'); // 1 run for NB
    });
    expect(mockedSaveMatch).toHaveBeenCalledTimes(1);
    let saveAfterNB: Match = mockedSaveMatch.mock.calls[0][0];
    expect(saveAfterNB.firstInnings!.score).toBe(1);
    expect(saveAfterNB.firstInnings!.timeline).toEqual(['NO_BALL']);
    const bowlerPAfterNB = saveAfterNB.teamBPlayers.find(p => p.id === baseMatchData.teamBPlayers[0].id);
    expect(bowlerPAfterNB!.bowlingStats.noBallsBowled).toBe(1);
    expect(bowlerPAfterNB!.bowlingStats.runsConceded).toBe(1);


    // 4. Attempt a "Wicket" on the Free Hit delivery
    const wicketButton = screen.getByText('Wicket');
    fireEvent.press(wicketButton);

    // 5. Verify UI and state updates after the "Wicket" on Free Hit
    await waitFor(() => {
      // Ensure we haven't incorrectly transitioned to 2nd innings batsman selection
      expect(screen.queryByText('Select Opening Batsmen')).toBeNull(); 
      // If the above passes, then check the scoring details
      expect(screen.getByTestId('total-score').props.children.join('')).toBe('1/0');
      expect(screen.getByText(`Striker: ${baseMatchData.teamAPlayers[0].name}* (0/1)`)).toBeTruthy();
    });

    expect(mockedSaveMatch).toHaveBeenCalledTimes(2);
    const saveAfterWicketOnFreeHit: Match = mockedSaveMatch.mock.calls[1][0];

    expect(saveAfterWicketOnFreeHit.firstInnings!.score).toBe(1); 
    expect(saveAfterWicketOnFreeHit.firstInnings!.wickets).toBe(0); 
    expect(saveAfterWicketOnFreeHit.firstInnings!.timeline).toEqual(['NO_BALL', 'WICKET']); 
    expect(saveAfterWicketOnFreeHit.firstInnings!.ballsInCurrentOver).toBe(1); 

    const strikerPAfterWicketOnFreeHit = saveAfterWicketOnFreeHit.teamAPlayers.find(p => p.id === baseMatchData.teamAPlayers[0].id);
    expect(strikerPAfterWicketOnFreeHit!.battingStats.status).toBe('not out'); 
    expect(strikerPAfterWicketOnFreeHit!.battingStats.runsScored).toBe(0);
    expect(strikerPAfterWicketOnFreeHit!.battingStats.ballsFaced).toBe(1); 

    const bowlerPAfterWicketOnFreeHit = saveAfterWicketOnFreeHit.teamBPlayers.find(p => p.id === baseMatchData.teamBPlayers[0].id);
    expect(bowlerPAfterWicketOnFreeHit!.bowlingStats.runsConceded).toBe(1); 
    expect(bowlerPAfterWicketOnFreeHit!.bowlingStats.noBallsBowled).toBe(1);
    expect(bowlerPAfterWicketOnFreeHit!.bowlingStats.wicketsTaken).toBe(0); 
    expect(bowlerPAfterWicketOnFreeHit!.bowlingStats.ballsBowled).toBe(1); 
  });

  it('handles a run-out correctly, allowing selection of dismissed batsman', async () => {
    mockCurrentRouteParams = { matchId: 'test-match-id-runout' };
    const matchDataForRunOut: Match = JSON.parse(JSON.stringify({
      ...baseMatchData,
      id: 'test-match-id-runout',
      oversPerInnings: 1,
      playersPerTeam: 3, // Allow for a next batsman
      teamAPlayers: [ // Team Strikers
        { id: 'pA1', name: 'Striker A1', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' as PlayerBattingStats['status'] }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
        { id: 'pA2', name: 'Striker A2', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' as PlayerBattingStats['status'] }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
        { id: 'pA3', name: 'Striker A3', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' as PlayerBattingStats['status'] }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
      ],
      teamBPlayers: [ // Team Bowlers
        { id: 'pB1', name: 'Bowler B1', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' as PlayerBattingStats['status'] }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
        { id: 'pB2', name: 'Bowler B2', battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'not out' as PlayerBattingStats['status'] }, bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 } },
      ],
      firstInnings: {
        ...baseMatchData.firstInnings,
        strikerId: 'pA1', // Striker A1
        nonStrikerId: 'pA2', // Striker A2
        currentBowlerId: 'pB1', // Bowler B1
        score: 0,
        wickets: 0,
        oversCompleted: 0,
        ballsInCurrentOver: 0,
        currentOverHistory: [],
        timeline: [],
        runsConcededThisOverMaiden: 0,
      }
    }));
    // Deep clone players to avoid test-to-test interference with mutable objects
    matchDataForRunOut.teamAPlayers = JSON.parse(JSON.stringify(matchDataForRunOut.teamAPlayers));
    matchDataForRunOut.teamBPlayers = JSON.parse(JSON.stringify(matchDataForRunOut.teamBPlayers));


    mockedLoadMatch.mockResolvedValueOnce(matchDataForRunOut);

    render(<MatchScoringScreen />);

    // 1. Wait for main scoring interface
    await waitFor(() => {
      expect(screen.getByText('Striker: Striker A1* (0/0)')).toBeTruthy();
      expect(screen.getByText('Non-Striker: Striker A2* (0/0)')).toBeTruthy(); 
    });

    // 2. Batsmen attempt a single (pA1 on strike)
    const oneRunButton = screen.getByText('1');
    fireEvent.press(oneRunButton);

    await waitFor(() => {
      expect(screen.getByTestId('total-score').props.children.join('')).toBe('1/0');
      expect(screen.getByText('Striker: Striker A2* (0/0)')).toBeTruthy(); 
      expect(screen.getByText(/Non-Striker: Striker A1\* \(1\/1\)/)).toBeTruthy(); 
    });

    expect(mockedSaveMatch).toHaveBeenCalledTimes(1);
    let saveAfterRun: Match = mockedSaveMatch.mock.calls[0][0];
    expect(saveAfterRun.firstInnings!.score).toBe(1);
    expect(saveAfterRun.firstInnings!.strikerId).toBe('pA2'); 

    // 3. Now, a Run Out occurs. Press "Wicket" button.
    const wicketButton = screen.getByText('Wicket');
    fireEvent.press(wicketButton);

    // 4. EXPECTATION: Component should prompt to select which batsman was run out.
    await waitFor(() => {
        expect(screen.getByText(/Who was run out\?/i)).toBeTruthy(); 
    });
    
    // Simulate selecting Striker A2 (who was the new striker after the single) as out.
    const batsmanOutButtonPA2 = screen.getByText('Striker A2'); 
    fireEvent.press(batsmanOutButtonPA2);


    // 5. Verify UI and state after Striker A2 is run out
    await waitFor(() => {
      expect(screen.getByTestId('total-score').props.children.join('')).toBe('1/1');
      expect(screen.getByText('Select Next Batsman')).toBeTruthy();
    });

    expect(mockedSaveMatch).toHaveBeenCalledTimes(2); 
    const finalSave: Match = mockedSaveMatch.mock.calls[1][0];

    expect(finalSave.firstInnings!.score).toBe(1); 
    expect(finalSave.firstInnings!.wickets).toBe(1);
    expect(finalSave.firstInnings!.timeline).toEqual([1, 'WICKET']); 

    const playerA1State = finalSave.teamAPlayers.find(p => p.id === 'pA1');
    expect(playerA1State!.battingStats.runsScored).toBe(1);
    expect(playerA1State!.battingStats.ballsFaced).toBe(1);
    expect(playerA1State!.battingStats.status).toBe('not out');

    const playerA2State = finalSave.teamAPlayers.find(p => p.id === 'pA2');
    expect(playerA2State!.battingStats.status).toBe('run out'); 
    expect(playerA2State!.battingStats.runsScored).toBe(0); 
    expect(playerA2State!.battingStats.ballsFaced).toBe(0); 

    const bowlerB1State = finalSave.teamBPlayers.find(p => p.id === 'pB1');
    expect(bowlerB1State!.bowlingStats.wicketsTaken).toBe(0); 
    expect(bowlerB1State!.bowlingStats.ballsBowled).toBe(1); 
    expect(bowlerB1State!.bowlingStats.runsConceded).toBe(1);
  });

  // More tests will be added here based on the component's functionality:
  // - Displaying correct innings information (batting team, score, overs)
  // - Handling ball-by-ball scoring input (runs, extras, wickets)
  // - Updating score, wickets, overs correctly
  // - Switching innings
  // - Ending match
  // - Displaying match summary/result
  // - Navigation based on match status/completion
  // - Handling undo functionality (if any)
  // - Displaying player stats (current batsman, bowler)
}); 