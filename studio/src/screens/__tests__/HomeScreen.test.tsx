import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import HomeScreen from '../HomeScreen';

// Mock react-navigation
const mockNavigate = jest.fn();
jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'), // Preserve other exports
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
}));

describe('HomeScreen', () => {
  beforeEach(() => {
    // Clear mock calls before each test
    mockNavigate.mockClear();
  });

  it('renders all essential static elements correctly', () => {
    const { getByText } = render(<HomeScreen />);

    // Check for titles and descriptions
    expect(getByText('Welcome to GullyScore!')).toBeTruthy();
    expect(getByText('Your companion for scoring gully cricket matches with ease and fun.')).toBeTruthy();
    expect(getByText('Why GullyScore?')).toBeTruthy();

    // Check for button texts
    expect(getByText('Start New Match')).toBeTruthy();
    expect(getByText('View Match History')).toBeTruthy();

    // Check for feature card titles
    expect(getByText('Quick Setup')).toBeTruthy();
    expect(getByText('Real-time Scoring')).toBeTruthy();
    expect(getByText('Match History')).toBeTruthy(); // This is also a feature card title
  });

  it('navigates to ConfigureMatch screen when "Start New Match" button is pressed', () => {
    const { getByText } = render(<HomeScreen />);
    fireEvent.press(getByText('Start New Match'));
    expect(mockNavigate).toHaveBeenCalledWith('ConfigureMatch');
  });

  it('navigates to MatchHistory screen when "View Match History" button is pressed', () => {
    const { getByText } = render(<HomeScreen />);
    fireEvent.press(getByText('View Match History'));
    expect(mockNavigate).toHaveBeenCalledWith('MatchHistory');
  });
}); 