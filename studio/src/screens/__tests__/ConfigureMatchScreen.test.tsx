import React from 'react';
import { render, fireEvent, waitFor, screen, act } from '@testing-library/react-native';
import { Alert } from 'react-native'; // Import Alert to access the mocked function
import ConfigureMatchScreen from '../ConfigureMatchScreen';
// REMOVED: import { mockAlertImplementation } from '../../../../jest.setup';

// Mock react-navigation
const mockNavigate = jest.fn();
const mockGoBack = jest.fn();

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
    goBack: mockGoBack,
  }),
  useRoute: () => ({
    params: {}, // Default empty params, can be overridden in tests
  }),
}));

// Mock storage utility
jest.mock('../../utils/storage', () => ({
  saveMatch: jest.fn(),
  // Add other storage functions if they were to be used, though unlikely for this screen
}));
const mockedSaveMatch = jest.mocked(require('../../utils/storage').saveMatch);

// REMOVED local jest.mock('react-native', ...) block

describe('ConfigureMatchScreen', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
    mockGoBack.mockClear();
    mockedSaveMatch.mockClear();
    // No Alert.alert.mockClear() needed here if we spy on it per test or use restoreAllMocks
  });

  afterEach(() => {
    jest.restoreAllMocks(); // Restore all spies and mocks after each test
  });

  it('renders correctly with initial default values', async () => {
    const { getByText, getByPlaceholderText, findByDisplayValue } = render(<ConfigureMatchScreen />);

    expect(getByPlaceholderText('E.g., Lions XI')).toBeTruthy();
    expect(getByPlaceholderText('E.g., Tigers XI')).toBeTruthy();
    expect(getByText('Overs per Innings (1-50)')).toBeTruthy();
    expect(await findByDisplayValue('5')).toBeTruthy();
    expect(getByText('Players per Team (1-11)')).toBeTruthy();
    expect(await findByDisplayValue('2')).toBeTruthy();
    expect(getByText('Last Man Standing')).toBeTruthy();
    expect(await screen.findByPlaceholderText('Team A Player 1 Name')).toBeTruthy();
    expect(await screen.findByPlaceholderText('Team A Player 2 Name')).toBeTruthy();
    expect(await screen.findByPlaceholderText('Team B Player 1 Name')).toBeTruthy();
    expect(await screen.findByPlaceholderText('Team B Player 2 Name')).toBeTruthy();

    expect(getByText('Start Match & Proceed to Toss')).toBeTruthy();
  });

  it('allows inputting team names', async () => {
    const { getByPlaceholderText } = render(<ConfigureMatchScreen />);
    const teamAInput = getByPlaceholderText('E.g., Lions XI');
    const teamBInput = getByPlaceholderText('E.g., Tigers XI');

    fireEvent.changeText(teamAInput, 'Team Avengers');
    fireEvent.changeText(teamBInput, 'Team Justice');

    expect(teamAInput.props.value).toBe('Team Avengers');
    expect(teamBInput.props.value).toBe('Team Justice');
  });

  it('allows changing number of overs', async () => {
    const { getByDisplayValue } = render(<ConfigureMatchScreen />); 
    const oversInput = await screen.findByDisplayValue('5');
    fireEvent.changeText(oversInput, '10');
    expect(oversInput.props.value).toBe('10');
  });

  it('allows changing players per team and updates player name fields', async () => {
    const { queryByPlaceholderText, findByDisplayValue, findByPlaceholderText } = render(<ConfigureMatchScreen />);    
    const playersInput = await findByDisplayValue('2'); 

    // Change to 1 player
    await act(async () => {
      fireEvent.changeText(playersInput, '1');
    });
    await waitFor(async () => {
        expect(await screen.findByPlaceholderText('Team A Player 1 Name')).toBeTruthy();
        expect(screen.queryByPlaceholderText('Team A Player 2 Name')).toBeNull();
        expect(await screen.findByPlaceholderText('Team B Player 1 Name')).toBeTruthy();
        expect(screen.queryByPlaceholderText('Team B Player 2 Name')).toBeNull();
        expect(screen.queryAllByText('Adjusting player fields...').length).toBe(0);
    }); // Default timeout

    // Change to 3 players
    await act(async () => {
        fireEvent.changeText(playersInput, '3');
    });
    await waitFor(async () => {
        expect(await screen.findByPlaceholderText('Team A Player 1 Name')).toBeTruthy();
        expect(await screen.findByPlaceholderText('Team A Player 2 Name')).toBeTruthy();
        expect(await screen.findByPlaceholderText('Team A Player 3 Name')).toBeTruthy();
        expect(await screen.findByPlaceholderText('Team B Player 1 Name')).toBeTruthy();
        expect(await screen.findByPlaceholderText('Team B Player 2 Name')).toBeTruthy();
        expect(await screen.findByPlaceholderText('Team B Player 3 Name')).toBeTruthy();
        expect(screen.queryAllByText('Adjusting player fields...').length).toBe(0);
    }); // Default timeout
  });

  it('allows toggling Last Man Standing switch', async () => {
    const { getByRole } = render(<ConfigureMatchScreen />); 
    // In React Native, a Switch often has an accessibilityRole of 'switch'
    const lmsSwitch = getByRole('switch'); 
    const initialValue = lmsSwitch.props.value; // Default is false
    
    await act(async () => {
        fireEvent(lmsSwitch, 'valueChange', !initialValue);
    });
    
    expect(lmsSwitch.props.value).toBe(!initialValue);

    // Toggle back
    await act(async () => {
        fireEvent(lmsSwitch, 'valueChange', initialValue);
    });
    expect(lmsSwitch.props.value).toBe(initialValue);
  });

  describe('Validation', () => {
    it('requires Team A and Team B names', async () => {
      const alertSpy = jest.spyOn(Alert, 'alert').mockImplementation(() => {});
      const { getByText, findByText } = render(<ConfigureMatchScreen />);      
      const proceedButton = getByText('Start Match & Proceed to Toss');

      await act(async () => {
        fireEvent.press(proceedButton);
      });
      
      expect(await findByText('Team A name is required')).toBeTruthy();
      expect(await findByText('Team B name is required')).toBeTruthy();
      // The onError handler will pick the first error (teamAName) and alert it.
      expect(alertSpy).toHaveBeenCalledWith("Validation Error", "Team A name is required");
    });

    it('requires Team names to be different', async () => {
      const alertSpy = jest.spyOn(Alert, 'alert').mockImplementation(() => {});
      const { getByText, getByPlaceholderText, findByText } = render(<ConfigureMatchScreen />);    
      const teamAInput = getByPlaceholderText('E.g., Lions XI');
      const teamBInput = getByPlaceholderText('E.g., Tigers XI');
      const proceedButton = getByText('Start Match & Proceed to Toss');

      fireEvent.changeText(teamAInput, 'Same Name');
      fireEvent.changeText(teamBInput, 'Same Name');
      
      // Fill player names to satisfy other validations
      await waitFor(() => { // Wait for initial player fields (default 2)
        expect(screen.getByPlaceholderText('Team A Player 1 Name')).toBeTruthy();
        expect(screen.getByPlaceholderText('Team A Player 2 Name')).toBeTruthy();
        expect(screen.queryAllByText('Adjusting player fields...').length).toBe(0);
      });
      fireEvent.changeText(await screen.findByPlaceholderText('Team A Player 1 Name'), 'P1');
      fireEvent.changeText(await screen.findByPlaceholderText('Team A Player 2 Name'), 'P2');
      fireEvent.changeText(await screen.findByPlaceholderText('Team B Player 1 Name'), 'P3');
      fireEvent.changeText(await screen.findByPlaceholderText('Team B Player 2 Name'), 'P4');

      await act(async () => { fireEvent.press(proceedButton); });
      
      // The schema validation for different team names should result in an inline error.
      // Zod refine error: errors.teamBName = { message: "Team names must be different", type: "custom"}
      // This custom error from Zod might not be directly shown by react-hook-form in the same way as field errors.
      // The component's getFirstError might pick this up from formState.errors.teamBName?.message.
      // If so, it would Alert.alert it.
      // Let's check the console log for formState.errors to see if it's there.
      // For now, let's assume it triggers an Alert.alert if it's a root/general form error.
      // The component logic: if (typeof firstError.message === 'string') { Alert.alert('Validation Error', firstError.message); }
      expect(alertSpy).toHaveBeenCalledWith('Validation Error', 'Team names must be different');
    });

    it('validates overs range (1-50)', async () => {
      const { getByText, findByText } = render(<ConfigureMatchScreen />);      
      const oversInput = await screen.findByDisplayValue('5'); // Default
      const proceedButton = getByText('Start Match & Proceed to Toss');
      
      // Test lower bound
      fireEvent.changeText(oversInput, '0');
      await act(async () => { fireEvent.press(proceedButton); });
      expect(await findByText('Overs must be between 1 and 50')).toBeTruthy();

      // Test upper bound
      fireEvent.changeText(oversInput, '51');
      await act(async () => { fireEvent.press(proceedButton); });
      expect(await findByText('Overs must be between 1 and 50')).toBeTruthy();
      
      // Test valid input clears error (implicitly by not finding error)
      fireEvent.changeText(oversInput, '10'); 
      // At this point, if other fields are not filled, pressing proceed might show other errors.
      // This test focuses on the overs error message appearing/disappearing.
      // A full form submission success test will confirm valid input works.
    });

    it('validates players per team range (1-11)', async () => {
      const { getByText, findByText } = render(<ConfigureMatchScreen />);      
      const playersInput = await screen.findByDisplayValue('2'); // Default
      const proceedButton = getByText('Start Match & Proceed to Toss');
      
      // Test lower bound (0 is invalid)
      fireEvent.changeText(playersInput, '0');
      await act(async () => { fireEvent.press(proceedButton); });
      expect(await findByText('Players must be between 1 and 11')).toBeTruthy();

      // Test upper bound (12 is invalid)
      fireEvent.changeText(playersInput, '12');
      await act(async () => { fireEvent.press(proceedButton); });
      expect(await findByText('Players must be between 1 and 11')).toBeTruthy();
    });

    it('validates players per team is >= 2 if Last Man Standing is false', async () => {
      const { getByText, findByText, getByRole, getByPlaceholderText } = render(<ConfigureMatchScreen />); 
      const playersInput = await screen.findByDisplayValue('2'); 
      const lmsSwitch = getByRole('switch');
      const proceedButton = getByText('Start Match & Proceed to Toss'); 

      expect(lmsSwitch.props.value).toBe(false);

      await act(async () => { 
        fireEvent.changeText(playersInput, '1'); 
      });
      
      await waitFor(async () => { 
        expect(await screen.findByPlaceholderText('Team A Player 1 Name')).toBeTruthy();
        expect(screen.queryByPlaceholderText('Team A Player 2 Name')).toBeNull();
        expect(await screen.findByPlaceholderText('Team B Player 1 Name')).toBeTruthy();
        expect(screen.queryByPlaceholderText('Team B Player 2 Name')).toBeNull();
        expect(screen.queryAllByText('Adjusting player fields...').length).toBe(0);
      });

      fireEvent.changeText(getByPlaceholderText('E.g., Lions XI'), 'Team Y'); 
      fireEvent.changeText(getByPlaceholderText('E.g., Tigers XI'), 'Team Z'); 
      fireEvent.changeText(await screen.findByPlaceholderText('Team A Player 1 Name'), 'Player AY1');
      fireEvent.changeText(await screen.findByPlaceholderText('Team B Player 1 Name'), 'Player BZ1');

      await act(async () => { fireEvent.press(proceedButton); });
      expect(await findByText("Players per team must be at least 2 if 'Last Man Standing' is disabled. For single player matches, enable 'Last Man Standing'.")).toBeTruthy();

      await act(async () => { fireEvent(lmsSwitch, 'valueChange', true); });
      
      // Configure mockSaveMatch to return a promise for the now-expected valid submission attempt
      mockedSaveMatch.mockResolvedValue(true);

      await act(async () => { fireEvent.press(proceedButton); });
      
      await waitFor(() => {
         expect(screen.queryByText("Players per team must be at least 2 if 'Last Man Standing' is disabled. For single player matches, enable 'Last Man Standing'.")).toBeNull();
      });
      expect(screen.queryByText("Team A name is required")).toBeNull();
      expect(screen.queryByText("Team B name is required")).toBeNull();
      expect(screen.queryByText("Player name is required")).toBeNull(); 
    });

    it('requires player names', async () => {
      const { getByText, findAllByText, getByPlaceholderText } = render(<ConfigureMatchScreen />);      
      const teamAInput = getByPlaceholderText('E.g., Lions XI');
      const teamBInput = getByPlaceholderText('E.g., Tigers XI');
      const proceedButton = getByText('Start Match & Proceed to Toss');

      fireEvent.changeText(teamAInput, 'Team P');
      fireEvent.changeText(teamBInput, 'Team Q');
      // Player names are initially empty strings by default (for 2 players)
      // So submitting without changing them should trigger the error
      
      await act(async () => { fireEvent.press(proceedButton); });
      
      // Expect multiple errors for empty player names (default 2 players per team)
      const playerNameRequiredErrors = await findAllByText('Player name is required');
      expect(playerNameRequiredErrors.length).toBeGreaterThanOrEqual(2); // At least for 2 players in one team, likely 4 total
    });

    it('requires player names to be unique within a team', async () => {
      const alertSpy = jest.spyOn(Alert, 'alert').mockImplementation(() => {});
      const { getByText, getByPlaceholderText } = render(<ConfigureMatchScreen />); // Removed findByText      
      fireEvent.changeText(getByPlaceholderText('E.g., Lions XI'), 'Team X');
      fireEvent.changeText(getByPlaceholderText('E.g., Tigers XI'), 'Team Y');
      
      // Ensure player fields are available (default 2 players)
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Team A Player 1 Name')).toBeTruthy();
        expect(screen.getByPlaceholderText('Team A Player 2 Name')).toBeTruthy();
        expect(screen.queryAllByText('Adjusting player fields...').length).toBe(0);
      });

      const playerA1Input = await screen.findByPlaceholderText('Team A Player 1 Name');
      fireEvent.changeText(playerA1Input, 'Duplicate Name');
      const playerA2Input = await screen.findByPlaceholderText('Team A Player 2 Name');
      fireEvent.changeText(playerA2Input, 'Duplicate Name'); // Duplicate

      const playerB1Input = await screen.findByPlaceholderText('Team B Player 1 Name');
      fireEvent.changeText(playerB1Input, 'Player B1');
      const playerB2Input = await screen.findByPlaceholderText('Team B Player 2 Name');
      fireEvent.changeText(playerB2Input, 'Player B2');

      const proceedButton = getByText('Start Match & Proceed to Toss');
      await act(async () => { fireEvent.press(proceedButton); });

      // The error message "Player names within Team A must be unique" comes from a Zod superRefine
      // and is added to ctx.addIssue with path: ["teamAPlayers"]. 
      // This typically results in formState.errors.teamAPlayers.message.
      // The component's onError handler should pick this up and show an Alert.
      // There won't be an inline text for this specific error, so we only check the alertSpy.
      expect(alertSpy).toHaveBeenCalledWith('Validation Error', 'Player names within Team A must be unique');
    });

  });

  describe('Submission', () => {
    it('successfully submits valid data and navigates to TossScreen', async () => {
      const alertSpy = jest.spyOn(Alert, 'alert').mockImplementation(() => {});
      mockedSaveMatch.mockResolvedValue(true);
      const { getByText, getByPlaceholderText, getByRole } = render(<ConfigureMatchScreen />); 

      fireEvent.changeText(getByPlaceholderText('E.g., Lions XI'), 'Team Alpha'); 
      fireEvent.changeText(getByPlaceholderText('E.g., Tigers XI'), 'Team Beta'); 
      const oversInput = await screen.findByDisplayValue('5');
      fireEvent.changeText(oversInput, '10');
      const playersInput = await screen.findByDisplayValue('2'); 
      
      await act(async () => { 
        fireEvent.changeText(playersInput, '1'); 
      }); 
      await act(async () => {
         const lmsSwitch = getByRole('switch');
         fireEvent(lmsSwitch, 'valueChange', true); 
      });
      
      await waitFor(async () => { 
        expect(await screen.findByPlaceholderText('Team A Player 1 Name')).toBeTruthy();
        expect(screen.queryByPlaceholderText('Team A Player 2 Name')).toBeNull();
        expect(await screen.findByPlaceholderText('Team B Player 1 Name')).toBeTruthy();
        expect(screen.queryByPlaceholderText('Team B Player 2 Name')).toBeNull();
        expect(screen.queryAllByText('Adjusting player fields...').length).toBe(0);
      });

      fireEvent.changeText(await screen.findByPlaceholderText('Team A Player 1 Name'), 'Alpha Player 1');
      fireEvent.changeText(await screen.findByPlaceholderText('Team B Player 1 Name'), 'Beta Player 1');

      const proceedButton = getByText('Start Match & Proceed to Toss'); 
      await act(async () => {
        fireEvent.press(proceedButton);
      });
      
      await waitFor(() => {
        expect(mockedSaveMatch).toHaveBeenCalledTimes(1);
      });
      
      const savedMatchData = mockedSaveMatch.mock.calls[0][0];
      expect(savedMatchData.teamAName).toBe('Team Alpha');
      expect(savedMatchData.teamBName).toBe('Team Beta');
      expect(savedMatchData.oversPerInnings).toBe(10);
      expect(savedMatchData.playersPerTeam).toBe(1);
      expect(savedMatchData.lastManStanding).toBe(true);
      expect(savedMatchData.teamAPlayers.length).toBe(1);
      expect(savedMatchData.teamAPlayers[0].name).toBe('Alpha Player 1');
      expect(savedMatchData.teamBPlayers.length).toBe(1);
      expect(savedMatchData.teamBPlayers[0].name).toBe('Beta Player 1');
      expect(savedMatchData.status).toBe('pending');

      expect(mockNavigate).toHaveBeenCalledWith('Toss', { matchId: savedMatchData.id });
      expect(alertSpy).not.toHaveBeenCalled();
    });

    it('shows an alert if saving match fails', async () => {
      const alertSpy = jest.spyOn(Alert, 'alert').mockImplementation(() => {});
      mockedSaveMatch.mockResolvedValue(false);
      const { getByText, getByPlaceholderText, getByRole } = render(<ConfigureMatchScreen />); 

      // Fill form with valid data
      fireEvent.changeText(getByPlaceholderText('E.g., Lions XI'), 'Team Gamma');
      fireEvent.changeText(getByPlaceholderText('E.g., Tigers XI'), 'Team Delta');
      const playerA1Input = await screen.findByPlaceholderText('Team A Player 1 Name');
      fireEvent.changeText(playerA1Input, 'Gamma Player 1');
      const playerA2Input = await screen.findByPlaceholderText('Team A Player 2 Name');
      fireEvent.changeText(playerA2Input, 'Gamma Player 2');
      const playerB1Input = await screen.findByPlaceholderText('Team B Player 1 Name');
      fireEvent.changeText(playerB1Input, 'Delta Player 1');
      const playerB2Input = await screen.findByPlaceholderText('Team B Player 2 Name');
      fireEvent.changeText(playerB2Input, 'Delta Player 2');

      const proceedButton = getByText('Start Match & Proceed to Toss');
      await act(async () => {
        fireEvent.press(proceedButton);
      });

      await waitFor(() => {
        expect(mockedSaveMatch).toHaveBeenCalledTimes(1);
      });
      expect(mockNavigate).not.toHaveBeenCalled();
      expect(alertSpy).toHaveBeenCalledWith('Storage Error', 'Could not save the match. Please try again.');
    });
  });

}); 