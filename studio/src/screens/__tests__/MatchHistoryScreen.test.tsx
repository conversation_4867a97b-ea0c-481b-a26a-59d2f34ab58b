import React from 'react';
import { render, fireEvent, waitFor, act } from '@testing-library/react-native';
import MatchHistoryScreen from '../MatchHistoryScreen';
import * as storage from '../../utils/storage';
import { Match, Player } from '../../types/matchTypes';

// Mock react-navigation
const mockNavigate = jest.fn();
let mockUseFocusEffectCallback: (() => void) | undefined;

jest.mock('@react-navigation/native', () => ({
  ...jest.requireActual('@react-navigation/native'),
  useNavigation: () => ({
    navigate: mockNavigate,
  }),
  useFocusEffect: jest.fn(callback => {
    mockUseFocusEffectCallback = callback;
    // The actual useEffect call is removed.
    // Tests should manually trigger the callback if needed for initial load,
    // or rely on components that trigger it upon focus.
    // Many tests already await loading states, which should cover this.
  }),
}));

// Mock storage functions
jest.mock('../../utils/storage');
const mockedLoadAllMatches = jest.mocked(storage.loadAllMatches);

// Helper to create mock player
const createMockPlayer = (id: string, name: string): Player => ({
  id,
  name,
  battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'did not bat' },
  bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 },
});

// Mock Matches
const mockMatch1: Match = {
  id: 'match-1',
  teamAName: 'Lions XI',
  teamBName: 'Tigers XI',
  oversPerInnings: 10,
  playersPerTeam: 1,
  lastManStanding: false,
  status: 'completed',
  createdAt: new Date('2024-05-20T10:00:00Z').getTime(),
  updatedAt: new Date('2024-05-20T12:00:00Z').getTime(),
  teamAPlayers: [createMockPlayer('p1a', 'Lion Player')],
  teamBPlayers: [createMockPlayer('p1b', 'Tiger Player')],
  resultDescription: 'Lions XI won by 5 wickets',
};

const mockMatch2: Match = {
  id: 'match-2',
  teamAName: 'Panthers',
  teamBName: 'Eagles',
  oversPerInnings: 8,
  playersPerTeam: 1,
  lastManStanding: false,
  status: 'completed',
  createdAt: new Date('2024-05-22T14:00:00Z').getTime(), // Newer than match1
  updatedAt: new Date('2024-05-22T16:00:00Z').getTime(),
  teamAPlayers: [createMockPlayer('p2a', 'Panther Player')],
  teamBPlayers: [createMockPlayer('p2b', 'Eagle Player')],
  resultDescription: 'Eagles won by 20 runs',
};

const mockMatch3Pending: Match = {
  id: 'match-3',
  teamAName: 'Warriors',
  teamBName: 'Knights',
  oversPerInnings: 12,
  playersPerTeam: 1,
  lastManStanding: false,
  status: 'pending', // Not completed
  createdAt: new Date('2024-05-21T10:00:00Z').getTime(),
  updatedAt: new Date('2024-05-21T11:00:00Z').getTime(),
  teamAPlayers: [createMockPlayer('p3a', 'Warrior Player')],
  teamBPlayers: [createMockPlayer('p3b', 'Knight Player')],
};

describe('MatchHistoryScreen', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
    mockedLoadAllMatches.mockReset();
    mockUseFocusEffectCallback = undefined; // Reset callback
  });

  it('shows loading indicator initially', () => {
    // Prevent loadAllMatches from resolving immediately to see loading state
    mockedLoadAllMatches.mockImplementationOnce(() => new Promise(() => {})); 
    const { getByText, getByTestId } = render(<MatchHistoryScreen />); // Assuming ActivityIndicator has a testID or use getByText
    expect(getByText('Loading match history...')).toBeTruthy();
    // If ActivityIndicator is used directly you might need a more specific query or testID
  });

  it('fetches and displays completed matches, sorted by date (newest first)', async () => {
    mockedLoadAllMatches.mockResolvedValueOnce([mockMatch1, mockMatch2, mockMatch3Pending]);
    const { getByText, queryByText } = render(<MatchHistoryScreen />); 

    // Manually trigger the focus effect
    if (mockUseFocusEffectCallback) {
      await act(async () => {
        await mockUseFocusEffectCallback!();
      });
    }

    // Wait for loading to finish
    await waitFor(() => expect(queryByText('Loading match history...')).toBeNull());

    // Check that only completed matches are shown and in correct order (match2 is newer)
    const match2Text = `${mockMatch2.teamAName} vs ${mockMatch2.teamBName}`;
    const match1Text = `${mockMatch1.teamAName} vs ${mockMatch1.teamBName}`;

    const match2DateString = new Date(mockMatch2.createdAt!).toLocaleDateString() + ' ' + new Date(mockMatch2.createdAt!).toLocaleTimeString();
    const match1DateString = new Date(mockMatch1.createdAt!).toLocaleDateString() + ' ' + new Date(mockMatch1.createdAt!).toLocaleTimeString();

    // Check for match2 details first (newest)
    expect(getByText(match2Text)).toBeTruthy();
    expect(getByText(match2DateString)).toBeTruthy(); 
    expect(getByText(mockMatch2.resultDescription!)).toBeTruthy();

    // Check for match1 details
    expect(getByText(match1Text)).toBeTruthy();
    expect(getByText(match1DateString)).toBeTruthy(); 
    expect(getByText(mockMatch1.resultDescription!)).toBeTruthy();

    // Check that pending match is not displayed
    expect(queryByText(`${mockMatch3Pending.teamAName} vs ${mockMatch3Pending.teamBName}`)).toBeNull();
  });

  it('displays "No completed matches found." if no completed matches are available', async () => {
    mockedLoadAllMatches.mockResolvedValueOnce([mockMatch3Pending]); // Only a pending match
    const { getByText, queryByText } = render(<MatchHistoryScreen />);    

    // Manually trigger the focus effect
    if (mockUseFocusEffectCallback) {
      await act(async () => {
        await mockUseFocusEffectCallback!();
      });
    }
    await waitFor(() => expect(queryByText('Loading match history...')).toBeNull());
    expect(getByText('No completed matches found.')).toBeTruthy();
  });

  it('displays "No completed matches found." if storage returns empty array', async () => {
    mockedLoadAllMatches.mockResolvedValueOnce([]); // No matches at all
    const { getByText, queryByText } = render(<MatchHistoryScreen />);    

    // Manually trigger the focus effect
    if (mockUseFocusEffectCallback) {
      await act(async () => {
        await mockUseFocusEffectCallback!();
      });
    }
    await waitFor(() => expect(queryByText('Loading match history...')).toBeNull());
    expect(getByText('No completed matches found.')).toBeTruthy();
  });

  it('navigates to MatchSummary on item press', async () => {
    mockedLoadAllMatches.mockResolvedValueOnce([mockMatch1]);
    const { getByText, queryByText } = render(<MatchHistoryScreen />);    

    // Manually trigger the focus effect
    if (mockUseFocusEffectCallback) {
      await act(async () => {
        await mockUseFocusEffectCallback!();
      });
    }
    await waitFor(() => expect(queryByText('Loading match history...')).toBeNull());
    
    fireEvent.press(getByText(`${mockMatch1.teamAName} vs ${mockMatch1.teamBName}`));
    expect(mockNavigate).toHaveBeenCalledWith('MatchSummary', { matchId: mockMatch1.id });
  });

  it('handles error when loading matches and shows empty state', async () => {
    mockedLoadAllMatches.mockRejectedValueOnce(new Error('Failed to fetch'));
    const { getByText, queryByText } = render(<MatchHistoryScreen />);    

    // Manually trigger the focus effect
    if (mockUseFocusEffectCallback) {
      await act(async () => {
        await mockUseFocusEffectCallback!(); // This will cause a console.error, which is expected for this test
      });
    }
    await waitFor(() => expect(queryByText('Loading match history...')).toBeNull());
    // As we are not mocking Alert, we verify the outcome: shows empty state
    expect(getByText('No completed matches found.')).toBeTruthy(); 
  });
  
  it('formats date correctly', async () => {
    mockedLoadAllMatches.mockResolvedValueOnce([mockMatch1]);
    const { getByText, queryByText } = render(<MatchHistoryScreen />); 

    // Manually trigger the focus effect
    if (mockUseFocusEffectCallback) {
      await act(async () => {
        await mockUseFocusEffectCallback!();
      });
    }
    await waitFor(() => expect(queryByText('Loading match history...')).toBeNull());

    const expectedDateString = new Date(mockMatch1.createdAt!).toLocaleDateString() + ' ' + new Date(mockMatch1.createdAt!).toLocaleTimeString();
    expect(getByText(expectedDateString)).toBeTruthy();
  });

  it('shows "Date N/A" if createdAt is 0 (or falsy)', async () => {
    const matchWithZeroDate: Match = { 
        ...mockMatch1, 
        id: 'match-zero-date', 
        createdAt: 0, // Test case for formatDate where timestamp might be 0
        resultDescription: "Match with zero date" // ensure unique text for getByText
    };
    mockedLoadAllMatches.mockResolvedValueOnce([matchWithZeroDate]);
    const { getByText, queryByText } = render(<MatchHistoryScreen />); 

    // Manually trigger the focus effect
    if (mockUseFocusEffectCallback) {
      await act(async () => {
        await mockUseFocusEffectCallback!();
      });
    }
    await waitFor(() => expect(queryByText('Loading match history...')).toBeNull());
    expect(getByText('Date N/A')).toBeTruthy();
    expect(getByText(matchWithZeroDate.resultDescription!)).toBeTruthy(); // ensure item is rendered
  });

}); 