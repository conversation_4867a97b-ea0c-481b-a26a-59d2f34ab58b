import React, { useState, useEffect } from 'react';
import { View, Text, TextInput, Button, StyleSheet, ScrollView, KeyboardAvoidingView, Platform, Alert, Switch } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ScreenNavigationProp, TossScreenParams } from '../navigation/AppNavigator'; // Adjust path as needed
import { Player, generatePlayerId, initialPlayerBattingStats, initialPlayerBowlingStats, Match } from '../types/matchTypes';
import { saveMatch } from '../utils/storage';
import { useForm, Controller, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

// Default values
const DEFAULT_OVERS = 5;
const DEFAULT_PLAYERS_PER_TEAM = 2;
const MIN_PLAYERS = 1;
const MAX_PLAYERS = 11;

// Zod Schema for form validation

// 1. Define the base schema without object-level refinements
const baseMatchConfigSchema = z.object({
  teamAName: z.string().trim().min(1, { message: 'Team A name is required' }),
  teamBName: z.string().trim().min(1, { message: 'Team B name is required' }),
  maxOvers: z.union([z.string(), z.number()]) // Accept string or number initially
    .refine(val => {
      const num = typeof val === 'string' ? parseInt(val, 10) : val;
      return !isNaN(num);
    }, { message: 'Overs must be a valid number' })
    .transform(val => typeof val === 'string' ? parseInt(val, 10) : val)
    .refine(val => val >= 1 && val <= 50, { message: 'Overs must be between 1 and 50' }),
  playersPerTeam: z.union([z.string(), z.number()]) // Accept string or number initially
    .refine(val => {
      const num = typeof val === 'string' ? parseInt(val, 10) : val;
      return !isNaN(num);
    }, { message: 'Players must be a valid number' })
    .transform(val => typeof val === 'string' ? parseInt(val, 10) : val)
    .refine(val => val >= MIN_PLAYERS && val <= MAX_PLAYERS, { message: `Players must be between ${MIN_PLAYERS} and ${MAX_PLAYERS}` }),
  lastManStanding: z.boolean(),
  teamAPlayers: z.array(z.string().trim().min(1, { message: "Player name is required" })), // Stricter base type
  teamBPlayers: z.array(z.string().trim().min(1, { message: "Player name is required" })), // Stricter base type
});

// Apply object-level refinements. matchConfigSchema will be a ZodEffects type.
const matchConfigSchema = baseMatchConfigSchema.superRefine((data, ctx) => {
  // Team names must be different
  if (data.teamAName.toLowerCase() === data.teamBName.toLowerCase()) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Team names must be different",
      path: ["teamBName"],
    });
  }

  // Players per team validation based on Last Man Standing
  if (!data.lastManStanding && data.playersPerTeam < 2) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Players per team must be at least 2 if 'Last Man Standing' is disabled. For single player matches, enable 'Last Man Standing'.",
      path: ["playersPerTeam"],
    });
  }

  // Team A player name uniqueness (names are already guaranteed non-empty by base schema)
  const teamAPlayerNames = data.teamAPlayers.map(name => name.toLowerCase()); // Already trimmed by Zod
  if (new Set(teamAPlayerNames).size !== teamAPlayerNames.length) {
    ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Player names within Team A must be unique",
        path: ["teamAPlayers"],
    });
  }

  // Team B player name uniqueness (names are already guaranteed non-empty by base schema)
  const teamBPlayerNames = data.teamBPlayers.map(name => name.toLowerCase()); // Already trimmed by Zod
  if (new Set(teamBPlayerNames).size !== teamBPlayerNames.length) {
    ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Player names within Team B must be unique",
        path: ["teamBPlayers"],
    });
  }
});

// Define MatchConfigFormData from the final schema including superRefine
export type MatchConfigFormData = z.infer<typeof matchConfigSchema>;

// Updated to reflect navigation with matchId
type ConfigureMatchNavigationProp = ScreenNavigationProp<'Toss'>;

export default function ConfigureMatchScreen() {
  const navigation = useNavigation<ConfigureMatchNavigationProp>();
  const [isUpdatingPlayerFields, setIsUpdatingPlayerFields] = useState(false); // New loading state

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    trigger,
  } = useForm<MatchConfigFormData>({
    resolver: zodResolver(matchConfigSchema as any),
    defaultValues: {
      teamAName: '',
      teamBName: '',
      maxOvers: DEFAULT_OVERS,
      playersPerTeam: DEFAULT_PLAYERS_PER_TEAM,
      lastManStanding: false,
      teamAPlayers: Array(DEFAULT_PLAYERS_PER_TEAM).fill(''),
      teamBPlayers: Array(DEFAULT_PLAYERS_PER_TEAM).fill(''),
    },
  });

  const watchedPlayersPerTeam = watch("playersPerTeam");
  const watchedTeamAPlayers = watch("teamAPlayers");
  const watchedTeamBPlayers = watch("teamBPlayers");

  useEffect(() => {
    setIsUpdatingPlayerFields(true); // Set loading true at the start

    const numPlayers = parseInt(String(watchedPlayersPerTeam), 10); // Ensure number
    
    if (isNaN(numPlayers) || numPlayers < MIN_PLAYERS || numPlayers > MAX_PLAYERS) {
      setValue('teamAPlayers', []);
      setValue('teamBPlayers', []);
      Promise.resolve().then(() => setIsUpdatingPlayerFields(false));
      return;
    }

    const adjustPlayerArray = (currentPlayers: string[], newSize: number) => {
      const newPlayers = [...currentPlayers];
      if (newPlayers.length < newSize) {
        while (newPlayers.length < newSize) newPlayers.push('');
      } else if (newPlayers.length > newSize) {
        newPlayers.length = newSize;
      }
      return newPlayers;
    };

    // adjustPlayerArray uses the watchedTeamAPlayers/BPlayers from the outer scope (captured in closure for this effect run)
    const newTeamAPlayersArray = adjustPlayerArray(watchedTeamAPlayers || [], numPlayers);
    const newTeamBPlayersArray = adjustPlayerArray(watchedTeamBPlayers || [], numPlayers);

    setValue('teamAPlayers', newTeamAPlayersArray);
    setValue('teamBPlayers', newTeamBPlayersArray);
    
    if (numPlayers > 0) {
        trigger("teamAPlayers");
        trigger("teamBPlayers");
    }

    // setIsUpdatingPlayerFields(false) synchronously
    setIsUpdatingPlayerFields(false);
    
  }, [watchedPlayersPerTeam, setValue, trigger]); // Dependencies: only the trigger (watchedPlayersPerTeam) and stable RHF functions

  const onSubmit = (data: MatchConfigFormData) => {
    // Data is already validated by Zod and transformed (e.g., strings to numbers)
    console.log('Form Data Submitted:', data);

    const createPlayers = (names: string[], teamPrefix: string): Player[] => {
      return names.map((name, index) => ({
        id: generatePlayerId(teamPrefix, index),
        name: name, // Already trimmed by Zod schema if .trim() is used there
        battingStats: initialPlayerBattingStats(),
        bowlingStats: initialPlayerBowlingStats(),
      }));
    };

    const teamAPlayersObjects = createPlayers(data.teamAPlayers, 'A');
    const teamBPlayersObjects = createPlayers(data.teamBPlayers, 'B');
    
    const matchId = `match_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    const newMatch: Match = {
      id: matchId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      status: 'pending',
      teamAName: data.teamAName,
      teamBName: data.teamBName,
      oversPerInnings: data.maxOvers, // Already a number due to Zod transform
      playersPerTeam: data.playersPerTeam, // Already a number
      lastManStanding: data.lastManStanding,
      teamAPlayers: teamAPlayersObjects,
      teamBPlayers: teamBPlayersObjects,
    };

    saveMatch(newMatch).then(success => {
      if (success) {
        navigation.navigate('Toss', { matchId: newMatch.id });
      } else {
        Alert.alert("Storage Error", "Could not save the match. Please try again.");
      }
    });
  };
  
  const onError = (formErrors: any) => {
    console.log("Form Errors:", formErrors);
    
    const firstErrorKey = Object.keys(formErrors)[0];
    if (!firstErrorKey) return;

    const firstErrorObject = formErrors[firstErrorKey];

    if (firstErrorObject) {
      if (typeof firstErrorObject.message === 'string') { // Standard field error like teamAName, maxOvers
        Alert.alert('Validation Error', firstErrorObject.message);
      } else if (firstErrorObject.root && typeof firstErrorObject.root.message === 'string') { // For array root errors (e.g., teamAPlayers uniqueness)
        Alert.alert('Validation Error', firstErrorObject.root.message);
      } else if (Array.isArray(firstErrorObject)) {
        // This case might be for when the error object itself is an array of errors, not typical for RHF structure with Zod.
        // If teamAPlayers errors were structured as formErrors.teamAPlayers = [ {message:...}, ...]
        // For now, let's assume individual player name errors (e.g. teamAPlayers.0.message) are shown inline and not alerted generally.
      } else {
        // Fallback or more complex error structure? For now, let inline errors handle it.
        // Consider if individual player name errors (e.g., formErrors.teamAPlayers[0].message) should be alerted if no other error is caught.
        // Example: iterate Object.values(firstErrorObject) if it's an object containing indexed errors
        const firstNestedError = Object.values(firstErrorObject)[0] as any;
        if (firstNestedError && typeof firstNestedError.message === 'string') {
            Alert.alert('Validation Error', firstNestedError.message); // Catches first player name error if teamAPlayers is the firstErrorObject
        }
      }
    }
  };

  const renderPlayerInputs = (team: 'A' | 'B') => {
    const fieldName = team === 'A' ? "teamAPlayers" : "teamBPlayers";
    const playersArray = team === 'A' ? watchedTeamAPlayers : watchedTeamBPlayers;
    const numPlayersToShow = parseInt(String(watchedPlayersPerTeam), 10);

    if (isNaN(numPlayersToShow) || numPlayersToShow < MIN_PLAYERS || numPlayersToShow > MAX_PLAYERS) {
      return null; 
    }

    // Use the explicit loading state
    if (isUpdatingPlayerFields) {
        return <Text style={styles.errorText}>Adjusting player fields...</Text>;
    }
    
    // Fallback check, in case isUpdatingPlayerFields turns false too early 
    // OR if the watched arrays are not yet updated for the *very first* population after loading.
    if (!playersArray || playersArray.length !== numPlayersToShow) {
        // This might still show "Adjusting..." if watched arrays are not immediately in sync after setIsUpdatingPlayerFields(false)
        // but before the next render that uses the correct playersArray length.
        // For the test, we hope that waiting for isUpdatingPlayerFields to be false is enough.
        return <Text style={styles.errorText}>Adjusting player fields...</Text>; 
    }

    return playersArray.map((_, index) => (
      <View key={`${fieldName}-${index}`} style={styles.inputGroupSmall}>
        <Controller
          control={control}
          name={`${fieldName}.${index}` as any} // Type assertion might be needed for indexed field names
          render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
            <>
              <TextInput
                style={styles.input}
                placeholder={`Team ${team} Player ${index + 1} Name`}
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
                autoCapitalize="words"
              />
              {error && <Text style={styles.errorText}>{error.message}</Text>}
            </>
          )}
        />
      </View>
    ));
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={styles.keyboardAvoidingContainer}
    >
      <ScrollView contentContainerStyle={styles.container} keyboardShouldPersistTaps="handled">
        <Text style={styles.title}>Configure New Match</Text>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Team A Name</Text>
          <Controller
            control={control}
            name="teamAName"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <>
                <TextInput style={styles.input} placeholder="E.g., Lions XI" onBlur={onBlur} onChangeText={onChange} value={value} autoCapitalize="words" />
                {error && <Text style={styles.errorText}>{error.message}</Text>}
              </>
            )}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Team B Name</Text>
          <Controller
            control={control}
            name="teamBName"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <>
                <TextInput style={styles.input} placeholder="E.g., Tigers XI" onBlur={onBlur} onChangeText={onChange} value={value} autoCapitalize="words" />
                {error && <Text style={styles.errorText}>{error.message}</Text>}
              </>
            )}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Overs per Innings (1-50)</Text>
          <Controller
            control={control}
            name="maxOvers"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <>
                <TextInput style={styles.input} placeholder={`E.g., ${DEFAULT_OVERS}`} onBlur={onBlur} onChangeText={onChange} value={String(value)} keyboardType="number-pad" />
                {error && <Text style={styles.errorText}>{error.message}</Text>}
              </>
            )}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>{`Players per Team (${MIN_PLAYERS}-${MAX_PLAYERS})`}</Text>
          <Controller
            control={control}
            name="playersPerTeam"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <>
                <TextInput style={styles.input} placeholder={`E.g., ${DEFAULT_PLAYERS_PER_TEAM}`} onBlur={onBlur} onChangeText={onChange} value={String(value)} keyboardType="number-pad" />
                {error && <Text style={styles.errorText}>{error.message}</Text>}
              </>
            )}
          />
        </View>
        
        <View style={styles.switchGroup}>
          <Text style={styles.label}>Last Man Standing</Text>
          <Controller
            control={control}
            name="lastManStanding"
            render={({ field: { onChange, value } }) => (
              <Switch trackColor={{ false: "#767577", true: "#81b0ff" }} thumbColor={value ? "#4f46e5" : "#f4f3f4"} ios_backgroundColor="#3e3e3e" onValueChange={onChange} value={value} />
            )}
          />
        </View>
        {errors.lastManStanding && <Text style={styles.errorText}>{errors.lastManStanding.message}</Text>}

        <Text style={styles.sectionTitle}>Team A Players</Text>
        {renderPlayerInputs('A')}
        {/* Display general error for teamAPlayers array if Zod refine for uniqueness fails */}
        {errors.teamAPlayers && typeof errors.teamAPlayers.message === 'string' && <Text style={styles.errorText}>{errors.teamAPlayers.message}</Text>}


        <Text style={styles.sectionTitle}>Team B Players</Text>
        {renderPlayerInputs('B')}
        {errors.teamBPlayers && typeof errors.teamBPlayers.message === 'string' && <Text style={styles.errorText}>{errors.teamBPlayers.message}</Text>}


        <View style={styles.buttonContainer}>
          <Button title="Start Match & Proceed to Toss" onPress={handleSubmit(onSubmit, onError)} color="#4f46e5" />
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  keyboardAvoidingContainer: { flex: 1 },
  container: { flexGrow: 1, alignItems: 'center', padding: 20, backgroundColor: '#f3f4f6' }, // Removed justifyContent
  title: { fontSize: 24, fontWeight: 'bold', marginBottom: 20, color: '#1f2937', textAlign: 'center' },
  inputGroup: { width: '90%', marginBottom: 15 },
  inputGroupSmall: { width: '90%', marginBottom: 8 }, // For player name inputs
  label: { fontSize: 16, marginBottom: 8, color: '#374151', fontWeight: '500' },
  input: { backgroundColor: 'white', borderWidth: 1, borderColor: '#d1d5db', borderRadius: 8, paddingHorizontal: 15, paddingVertical: 12, fontSize: 16, width: '100%' },
  switchGroup: { width: '90%', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20, paddingVertical: 10 },
  sectionTitle: { fontSize: 18, fontWeight: '600', color: '#1f2937', marginTop: 20, marginBottom: 10, width: '90%' },
  buttonContainer: { width: '90%', marginTop: 25, marginBottom: 50 /* Ensure button is visible */ },
  errorText: { color: 'red', fontSize: 14, width: '90%', textAlign: 'center', marginBottom: 10 },
}); 