import React, { useState, useEffect, useReducer } from 'react';
import { View, Text, StyleSheet, Button, ScrollView, Alert } from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { RootStackParamList, MatchScoringParams, ScreenNavigationProp } from '../navigation/AppNavigator'; // Adjust path as needed
import { Player, BallOutcome, Match } from '../types/matchTypes'; // Added Match import
import { loadMatch, saveMatch } from '../utils/storage'; // Import storage functions

// Define UI States
export enum UiState {
  LOADING = 'LOADING',
  SELECTING_BATSMEN = 'SELECTING_BATSMEN',
  SELECTING_BOWLER = 'SELECTING_BOWLER',
  SCORING_ACTIVE = 'SCORING_ACTIVE',
  SELECTING_NEW_BATSMAN = 'SELECTING_NEW_BATSMAN',
  SELECTING_NEW_BOWLER = 'SELECTING_NEW_BOWLER',
  MATCH_COMPLETE = 'MATCH_COMPLETE',
  ERROR_LOADING = 'ERROR_LOADING'
}

// Define the type for the route params
type MatchScoringScreenRouteProp = RouteProp<RootStackParamList, 'MatchScoring'>;

// Enum for different types of ball outcomes - REMOVED FROM HERE

// --- Reducer Setup ---
interface ScreenState {
  uiState: UiState;
  currentMatch: Match | null;
  // Add error message to state to be displayed by Alert in a separate useEffect
  errorMessage: string | null; 
  // Flags for one-time alerts, managed by reducer or separate useState if simpler
  inning1ReadyAlertShown: boolean;
  inning2ReadyAlertShown: boolean;
}

const initialState: ScreenState = {
  uiState: UiState.LOADING,
  currentMatch: null,
  errorMessage: null,
  inning1ReadyAlertShown: false,
  inning2ReadyAlertShown: false,
};

type Action =
  | { type: 'FETCH_INIT' }
  | { type: 'FETCH_SUCCESS'; payload: Match }
  | { type: 'FETCH_ERROR'; payload: string }
  | { type: 'SET_UI_STATE'; payload: UiState } // Generic UI state setter
  | { type: 'UPDATE_MATCH_STATE'; payload: Match } // For any action that updates match
  | { type: 'SET_INNING1_ALERT_SHOWN' }
  | { type: 'SET_INNING2_ALERT_SHOWN'; payload: boolean }
  | { type: 'RESET_ERROR_MESSAGE' };


function screenReducer(state: ScreenState, action: Action): ScreenState {
  switch (action.type) {
    case 'FETCH_INIT':
      return { 
        ...initialState, // Reset to initial on new fetch if needed, or just set loading
        uiState: UiState.LOADING,
        currentMatch: null, // Clear previous match on new fetch
        errorMessage: null,
      };
    case 'FETCH_SUCCESS':
      const match = action.payload;
      let nextUiStateAfterFetch: UiState;
      const initialInningsData = match.status === 'live_inning1' ? match.firstInnings :
                               match.status === 'live_inning2' ? match.secondInnings : null;
      let needsBatsmenSelection = false;
      let needsBowlerSelection = false;

      if (initialInningsData) {
        if (!initialInningsData.strikerId || !initialInningsData.nonStrikerId) {
          needsBatsmenSelection = true;
        } else if (!initialInningsData.currentBowlerId) {
          needsBowlerSelection = true;
        }
      } else if (match.status === 'live_inning1' || match.status === 'live_inning2') {
        needsBatsmenSelection = true;
      }
      
      if (needsBatsmenSelection) nextUiStateAfterFetch = UiState.SELECTING_BATSMEN;
      else if (needsBowlerSelection) nextUiStateAfterFetch = UiState.SELECTING_BOWLER;
      else nextUiStateAfterFetch = UiState.SCORING_ACTIVE;
      
      return { ...state, currentMatch: match, uiState: nextUiStateAfterFetch, errorMessage: null };
    case 'FETCH_ERROR':
      return { ...state, uiState: UiState.ERROR_LOADING, currentMatch: null, errorMessage: action.payload };
    case 'SET_UI_STATE':
      return { ...state, uiState: action.payload };
    case 'UPDATE_MATCH_STATE':
      return { ...state, currentMatch: action.payload };
    case 'SET_INNING1_ALERT_SHOWN':
      return { ...state, inning1ReadyAlertShown: true };
    case 'SET_INNING2_ALERT_SHOWN':
      return { ...state, inning2ReadyAlertShown: action.payload };
    case 'RESET_ERROR_MESSAGE':
      return { ...state, errorMessage: null };
    default:
      return state;
  }
}
// --- End Reducer Setup ---

export default function MatchScoringScreen() {
  const route = useRoute<MatchScoringScreenRouteProp>();
  const navigation = useNavigation<ScreenNavigationProp<'MatchScoring'>>();
  
  const { matchId } = route.params;

  const [state, dispatch] = useReducer(screenReducer, initialState);
  const { uiState, currentMatch, errorMessage, inning1ReadyAlertShown, inning2ReadyAlertShown } = state;

  // State for selected players
  const [selectedStriker, setSelectedStriker] = useState<Player | null>(null);
  const [selectedNonStriker, setSelectedNonStriker] = useState<Player | null>(null);
  const [selectedBowler, setSelectedBowler] = useState<Player | null>(null);

  // --- DERIVED STATE ---
  // These are now derived from currentMatch for rendering purposes
  const innings = currentMatch?.status === 'live_inning1' ? currentMatch.firstInnings : currentMatch?.status === 'live_inning2' ? currentMatch.secondInnings : null;
  const battingTeamPlayers = innings?.battingTeamName === currentMatch?.teamAName ? currentMatch?.teamAPlayers : innings?.battingTeamName === currentMatch?.teamBName ? currentMatch?.teamBPlayers : [];
  const bowlingTeamPlayers = innings?.bowlingTeamName === currentMatch?.teamAName ? currentMatch?.teamAPlayers : innings?.bowlingTeamName === currentMatch?.teamBName ? currentMatch?.teamBPlayers : [];
  
  const striker = battingTeamPlayers?.find(p => p.id === innings?.strikerId);
  const nonStriker = battingTeamPlayers?.find(p => p.id === innings?.nonStrikerId);
  const currentBowler = bowlingTeamPlayers?.find(p => p.id === innings?.currentBowlerId);

  useEffect(() => {
    dispatch({ type: 'FETCH_INIT' });

    const performFetch = async () => {
      try {
        const loadedMatchData = await loadMatch(matchId);
        if (loadedMatchData) {
          dispatch({ type: 'FETCH_SUCCESS', payload: loadedMatchData });
        } else {
          // Explicitly return an error object structure
          dispatch({ type: 'FETCH_ERROR', payload: "Failed to load match data." });
        }
      } catch (e: any) {
        dispatch({ type: 'FETCH_ERROR', payload: e.message || "An unexpected error occurred during match loading." });
      }
    };

    performFetch();
  }, [matchId, dispatch]);

  // useEffect to handle showing Alert for error messages from the reducer
  useEffect(() => {
    if (errorMessage) {
      Alert.alert(
        "Error", 
        errorMessage, 
        [{ text: "OK", onPress: () => navigation.goBack() }]
      );
      dispatch({ type: 'RESET_ERROR_MESSAGE' }); // Reset error after showing
    }
  }, [errorMessage, navigation, dispatch]);

  useEffect(() => {
    // Only show "Match Ready!" alerts if scoring is actually active.
    if (uiState !== UiState.SCORING_ACTIVE) return;

    if (!currentMatch) return;

    // Condition for showing "Match Ready!" alert
    if (currentMatch.status === 'live_inning1' && innings?.strikerId && innings?.currentBowlerId && !inning1ReadyAlertShown) {
      Alert.alert(
        "Match Ready! (Innings 1)",
        `${innings.battingTeamName} (Batting) vs ${innings.bowlingTeamName} (Bowling).\nStriker: ${striker?.name}, Bowler: ${currentBowler?.name}.\n${currentMatch.oversPerInnings} overs match.`
      );
      dispatch({ type: 'SET_INNING1_ALERT_SHOWN' });
    } else if (currentMatch.status === 'live_inning2' && innings?.strikerId && innings?.currentBowlerId && !inning2ReadyAlertShown && currentMatch.secondInnings && currentMatch.secondInnings.target) {
      Alert.alert(
        "Match Ready! (Innings 2)",
        `${innings.battingTeamName} (Batting) vs ${innings.bowlingTeamName} (Bowling).\nTarget: ${currentMatch.secondInnings.target}. Striker: ${striker?.name}, Bowler: ${currentBowler?.name}.`
      );
      dispatch({ type: 'SET_INNING2_ALERT_SHOWN', payload: true });
    }
  }, [currentMatch, uiState, inning1ReadyAlertShown, inning2ReadyAlertShown, striker, currentBowler, innings, dispatch]);

  // Helper to get current innings details from currentMatch
  const getCurrentInningsDetails = () => {
    if (!currentMatch) return null;
    if (currentMatch.status === 'live_inning1') return currentMatch.firstInnings;
    if (currentMatch.status === 'live_inning2') return currentMatch.secondInnings;
    return null;
  };

  const updateAndSaveMatch = async (updatedMatchData: Partial<Match> | ((prevMatch: Match) => Match)) => {
    if (!currentMatch) return null;
    let newMatchState: Match;
    if (typeof updatedMatchData === 'function') {
      newMatchState = updatedMatchData(currentMatch);
    } else {
      newMatchState = { ...currentMatch, ...updatedMatchData, updatedAt: Date.now() };
    }
    
    // Ensure innings objects exist if status implies they should
    if (newMatchState.status === 'live_inning1' && !newMatchState.firstInnings) {
        // This case should ideally be handled when status transitions to live_inning1
        // console.error("Error: Match status is live_inning1 but firstInnings is undefined.");
        // Initialize a default firstInnings object if it's missing.
        // This is a safeguard; proper initialization should occur in TossScreen or earlier.
        newMatchState.firstInnings = {
            battingTeamName: newMatchState.tossWinnerTeamName === newMatchState.teamAName ? (newMatchState.decision === 'Bat' ? newMatchState.teamAName : newMatchState.teamBName) : (newMatchState.decision === 'Bat' ? newMatchState.teamBName : newMatchState.teamAName),
            bowlingTeamName: newMatchState.tossWinnerTeamName === newMatchState.teamAName ? (newMatchState.decision === 'Bowl' ? newMatchState.teamAName : newMatchState.teamBName) : (newMatchState.decision === 'Bowl' ? newMatchState.teamBName : newMatchState.teamAName),
            score: 0, wickets: 0, oversCompleted: 0, ballsInCurrentOver: 0, currentOverHistory: [], runsConcededThisOverMaiden: 0, timeline: []
        };
    }
    // Add similar check for secondInnings if status is live_inning2

    const success = await saveMatch(newMatchState);
    if (success) {
      dispatch({ type: 'UPDATE_MATCH_STATE', payload: newMatchState });
      return newMatchState;
    } else {
      Alert.alert("Storage Error", "Could not save match updates. Please check console.");
      return currentMatch; // Return old state on failure
    }
  };

  const handleSelectStriker = (player: Player) => {
    if (selectedNonStriker && selectedNonStriker.id === player.id) {
      Alert.alert("Selection Error", "Striker cannot be the same as Non-Striker.");
      return;
    }
    setSelectedStriker(player);
  };

  const handleSelectNonStriker = (player: Player) => {
    if (selectedStriker && selectedStriker.id === player.id) {
      Alert.alert("Selection Error", "Non-Striker cannot be the same as Striker.");
      return;
    }
    setSelectedNonStriker(player);
  };

  const handleConfirmBatsmen = async () => {
    if (!selectedStriker || !selectedNonStriker || !currentMatch) {
      Alert.alert("Selection Error", "Please select both Striker and Non-Striker.");
      return;
    }
    const updatedMatchWithBatsmen = await updateAndSaveMatch(prevMatch => {
      const updated = JSON.parse(JSON.stringify(prevMatch));
      if (updated.status === 'live_inning1' && updated.firstInnings) {
        updated.firstInnings.strikerId = selectedStriker.id;
        updated.firstInnings.nonStrikerId = selectedNonStriker.id;
      } else if (updated.status === 'live_inning2' && updated.secondInnings) {
        updated.secondInnings.strikerId = selectedStriker.id;
        updated.secondInnings.nonStrikerId = selectedNonStriker.id;
      }
      return updated;
    });
    if (updatedMatchWithBatsmen) {
        dispatch({ type: 'SET_UI_STATE', payload: UiState.SELECTING_BOWLER });
    }
  };

  const handleSelectBowler = (player: Player) => {
    setSelectedBowler(player);
  };

  const handleConfirmBowler = async () => {
    if (!selectedBowler || !currentMatch) {
      Alert.alert("Selection Error", "Please select an Opening Bowler.");
      return;
    }
    const updatedMatchWithBowler = await updateAndSaveMatch(prevMatch => {
      const updated = JSON.parse(JSON.stringify(prevMatch));
      if (updated.status === 'live_inning1' && updated.firstInnings) {
        updated.firstInnings.currentBowlerId = selectedBowler.id;
      } else if (updated.status === 'live_inning2' && updated.secondInnings) {
        updated.secondInnings.currentBowlerId = selectedBowler.id;
      }
      return updated;
    });
    if (updatedMatchWithBowler) {
        dispatch({ type: 'SET_UI_STATE', payload: UiState.SCORING_ACTIVE });
        setSelectedBowler(null); // Clear selection for next time
    }
  };

  const rotateStrikeInMatch = (matchData: Match) => {
    const currentInnings = matchData.status === 'live_inning1' ? matchData.firstInnings : matchData.secondInnings;
    if (!currentInnings || !currentInnings.strikerId || !currentInnings.nonStrikerId) return;
    
    const tempStrikerId = currentInnings.strikerId;
    currentInnings.strikerId = currentInnings.nonStrikerId;
    currentInnings.nonStrikerId = tempStrikerId;
  };

  const handleBallPlayed = async (outcome: BallOutcome) => {
    if (currentMatch?.status === 'completed') {
      Alert.alert("Match Already Over", currentMatch.resultDescription || "The match has already concluded.");
      return;
    }

    const inningsStateForCheck = currentMatch?.status === 'live_inning1' ? currentMatch.firstInnings : currentMatch?.secondInnings;

    let setupComplete = false;
    if (currentMatch && inningsStateForCheck && inningsStateForCheck.strikerId && inningsStateForCheck.currentBowlerId) {
        if (currentMatch.lastManStanding && inningsStateForCheck.wickets === currentMatch.playersPerTeam - 1) {
            // LMS scenario: last man is batting alone, nonStrikerId is expected to be null after (P-1)th wicket
            // This check ensures that the striker (last man) is set.
            setupComplete = true; 
        } else if (inningsStateForCheck.nonStrikerId) {
            // Normal scenario: nonStrikerId must be present
            setupComplete = true;
        }
    }

    if (!setupComplete) {
      Alert.alert("Setup Incomplete", "Please ensure opening batsmen (striker & non-striker, unless LMS last man) and bowler are selected.");
      // Attempt to re-trigger the correct selection UI
      if (currentMatch && inningsStateForCheck) {
        if (!inningsStateForCheck.strikerId || !inningsStateForCheck.nonStrikerId) {
            if (!(currentMatch.lastManStanding && inningsStateForCheck.wickets === currentMatch.playersPerTeam - 1 && inningsStateForCheck.strikerId && !inningsStateForCheck.nonStrikerId)) {
                setSelectedStriker(null);
                setSelectedNonStriker(null);
                dispatch({ type: 'SET_UI_STATE', payload: UiState.SELECTING_BATSMEN });
            }
        } else if (!inningsStateForCheck.currentBowlerId) {
          setSelectedBowler(null);
          dispatch({ type: 'SET_UI_STATE', payload: UiState.SELECTING_BOWLER });
        }
      }
      return;
    }
    
    // Existing check for resolved player objects (striker, currentBowler derived from IDs)
    if (!striker || !currentBowler) { // `innings` is also derived and checked implicitly by currentInningsStateForCheck
      Alert.alert("Error", "Match data or current players not fully loaded / resolved from IDs!");
      return;
    }

    const updatedMatch = JSON.parse(JSON.stringify(currentMatch)) as Match;
    const currentInningsState = updatedMatch.status === 'live_inning1' ? updatedMatch.firstInnings! : updatedMatch.secondInnings!;
    
    const bowlerTeamKey = currentInningsState.bowlingTeamName === updatedMatch.teamAName ? 'teamAPlayers' : 'teamBPlayers';
    const batterTeamKey = currentInningsState.battingTeamName === updatedMatch.teamAName ? 'teamAPlayers' : 'teamBPlayers';

    const bowlerInMatch = updatedMatch[bowlerTeamKey].find(p => p.id === currentInningsState.currentBowlerId);
    const batsmanWhoWasStrikerPlayerObj = updatedMatch[batterTeamKey].find(p => p.id === currentInningsState.strikerId);

    if (!bowlerInMatch || !batsmanWhoWasStrikerPlayerObj) {
        Alert.alert("Error", "Current bowler or striker not found in match data!");
        return;
    }

    // Determine max wickets based on LMS rule for the "last man can bat alone" interpretation
    const maxWicketsToEndInnings = updatedMatch.lastManStanding 
                                   ? updatedMatch.playersPerTeam 
                                   : (updatedMatch.playersPerTeam > 0 ? updatedMatch.playersPerTeam - 1 : 0);

    // --- Update Innings State ---
    currentInningsState.timeline.push(outcome);
    let legalDelivery = true;

    if (outcome === BallOutcome.WICKET) {
      currentInningsState.wickets++;
      currentInningsState.currentOverHistory.push('W');
      
      const nonStrikerIdAtTimeOfWicket = currentInningsState.nonStrikerId; 

      if (batsmanWhoWasStrikerPlayerObj) { 
        batsmanWhoWasStrikerPlayerObj.battingStats.status = 'out';
      }
      if (bowlerInMatch) {
        bowlerInMatch.bowlingStats.wicketsTaken++;
      }

      if (currentInningsState.wickets < maxWicketsToEndInnings) {
        if (updatedMatch.lastManStanding && currentInningsState.wickets === updatedMatch.playersPerTeam - 1) {
          if (nonStrikerIdAtTimeOfWicket) {
            const lastMan = updatedMatch[batterTeamKey].find(p => p.id === nonStrikerIdAtTimeOfWicket);
            if (lastMan && lastMan.battingStats.status !== 'out') { 
              currentInningsState.strikerId = nonStrikerIdAtTimeOfWicket;
              currentInningsState.nonStrikerId = null; 
            } else {
              const anyOtherLastMan = updatedMatch[batterTeamKey].find(p => p.battingStats.status !== 'out' && p.id !== batsmanWhoWasStrikerPlayerObj?.id);
              if (anyOtherLastMan) {
                currentInningsState.strikerId = anyOtherLastMan.id;
                currentInningsState.nonStrikerId = null;
              } else {
                // console.error("LMS Error: Last man scenario, but no available batsman found (original non-striker or alternative).");
                currentInningsState.strikerId = null; 
                currentInningsState.nonStrikerId = null;
              }
            }
          } else {
            const singlePlayerLastMan = updatedMatch[batterTeamKey].find(p => p.battingStats.status !== 'out' && p.id !== batsmanWhoWasStrikerPlayerObj?.id);
            if (singlePlayerLastMan) {
              currentInningsState.strikerId = singlePlayerLastMan.id;
              currentInningsState.nonStrikerId = null;
            } else {
              // console.error("LMS Error: Last man scenario (no non-striker prior), but no available batsman found.");
              currentInningsState.strikerId = null;
              currentInningsState.nonStrikerId = null;
            }
          }
        } else {
          if (batsmanWhoWasStrikerPlayerObj && currentInningsState.strikerId === batsmanWhoWasStrikerPlayerObj.id) {
             currentInningsState.strikerId = null; 
          }
        }
      }
    } else if (outcome === BallOutcome.WIDE) {
      currentInningsState.score++;
      bowlerInMatch.bowlingStats.runsConceded++;
      bowlerInMatch.bowlingStats.widesBowled++;
      currentInningsState.currentOverHistory.push('Wd');
      legalDelivery = false;
    } else if (outcome === BallOutcome.NO_BALL) {
      currentInningsState.score++;
      bowlerInMatch.bowlingStats.runsConceded++;
      bowlerInMatch.bowlingStats.noBallsBowled++;
      currentInningsState.currentOverHistory.push('Nb');
      legalDelivery = false; 
      // Note: Runs off a no-ball will be handled by the numeric outcome if it's also a scoring shot
    } else { // DOT, 1, 2, 3, 4, 6
      const runsScored = outcome as number;
      currentInningsState.score += runsScored;
      batsmanWhoWasStrikerPlayerObj.battingStats.runsScored += runsScored;
      bowlerInMatch.bowlingStats.runsConceded += runsScored;
      currentInningsState.runsConcededThisOverMaiden += runsScored;
      currentInningsState.currentOverHistory.push(String(runsScored));
      if (runsScored === 4) batsmanWhoWasStrikerPlayerObj.battingStats.fours++;
      if (runsScored === 6) batsmanWhoWasStrikerPlayerObj.battingStats.sixes++;
      
      // Batsmen rotation for odd runs
      if (runsScored === 1 || runsScored === 3) {
        rotateStrikeInMatch(updatedMatch);
      }
    }
    
    if (legalDelivery) {
      currentInningsState.ballsInCurrentOver++;
      batsmanWhoWasStrikerPlayerObj.battingStats.ballsFaced++;
      bowlerInMatch.bowlingStats.ballsBowled++;
    }

    // Over completion
    let overCompletedThisBall = false;
    if (currentInningsState.ballsInCurrentOver === 6) {
      overCompletedThisBall = true;
      currentInningsState.oversCompleted++;
      currentInningsState.ballsInCurrentOver = 0;
      
      // Maiden Over Check
      if (currentInningsState.runsConcededThisOverMaiden === 0) {
        bowlerInMatch.bowlingStats.maidensBowled++;
        Alert.alert("Maiden Over!", `${bowlerInMatch.name} bowled a maiden!`);
      }
      currentInningsState.runsConcededThisOverMaiden = 0; // Reset for next over
      
      Alert.alert("Over Complete!", `Over ${currentInningsState.oversCompleted} finished by ${bowlerInMatch.name}.`);
    }
    
    // --- Innings/Match End Check ---
    const currentMaxWickets = updatedMatch.lastManStanding 
                              ? updatedMatch.playersPerTeam 
                              : (updatedMatch.playersPerTeam > 0 ? updatedMatch.playersPerTeam - 1 : 0);
    let inningsOver = false;

    // Check for target achieved in second innings first
    if (updatedMatch.status === 'live_inning2' && updatedMatch.secondInnings && currentInningsState.score >= updatedMatch.secondInnings.target) {
        inningsOver = true;
        updatedMatch.matchWinnerTeamName = currentInningsState.battingTeamName;
        updatedMatch.resultDescription = `${currentInningsState.battingTeamName} won by ${updatedMatch.playersPerTeam - currentInningsState.wickets} wickets.`;
        updatedMatch.status = 'completed';
    } 
    // If target not yet achieved, check for other innings end conditions (wickets or overs)
    else if (currentInningsState.wickets >= currentMaxWickets || currentInningsState.oversCompleted === updatedMatch.oversPerInnings) {
      inningsOver = true;
      if (updatedMatch.status === 'live_inning1') {
        Alert.alert("Innings Over", `Score: ${currentInningsState.score}/${currentInningsState.wickets}. Target: ${currentInningsState.score + 1}`);
        updatedMatch.status = 'live_inning2';
        updatedMatch.secondInnings = {
          battingTeamName: currentInningsState.bowlingTeamName, 
          bowlingTeamName: currentInningsState.battingTeamName,
          score: 0, wickets: 0, oversCompleted: 0, ballsInCurrentOver: 0,
          currentOverHistory: [], runsConcededThisOverMaiden: 0, timeline: [],
          target: currentInningsState.score + 1,
          strikerId: undefined,
          nonStrikerId: undefined,
          currentBowlerId: undefined,
        };
        // UI state changes for selecting batsmen for 2nd innings will be handled after save
      } else if (updatedMatch.status === 'live_inning2' && updatedMatch.secondInnings) { // Ensure secondInnings exists
        // Match End Logic if target wasn't reached but wickets/overs ran out
        const secondInningsState = updatedMatch.secondInnings; // Use the non-null asserted version
        if (secondInningsState.score < secondInningsState.target - 1) { 
             updatedMatch.matchWinnerTeamName = secondInningsState.bowlingTeamName;
             updatedMatch.resultDescription = `${secondInningsState.bowlingTeamName} won by ${secondInningsState.target - 1 - secondInningsState.score} runs.`;
        } else if (secondInningsState.score === secondInningsState.target - 1) { 
            updatedMatch.resultDescription = "Match Tied!";
        } else { // Should not happen if score >= target is caught first
            updatedMatch.resultDescription = "Match Concluded (Draw/Other).";
        }
        updatedMatch.status = 'completed';
      }
    }

    const matchJustCompleted = inningsOver && updatedMatch.status === 'completed';
    const justTransitionedToSecondInnings = inningsOver && updatedMatch.status === 'live_inning2' && !matchJustCompleted;

    // Persist all accumulated changes to the match object
    const savedMatchAfterBall = await updateAndSaveMatch(updatedMatch);

    if (savedMatchAfterBall) {
        if (matchJustCompleted && savedMatchAfterBall.status === 'completed') {
            Alert.alert("Match Over!", savedMatchAfterBall.resultDescription || "Match Concluded.");
            dispatch({ type: 'SET_UI_STATE', payload: UiState.MATCH_COMPLETE });
            navigation.navigate('MatchSummary', { matchId: savedMatchAfterBall.id });
        } else if (matchJustCompleted && savedMatchAfterBall.status !== 'completed') {
            Alert.alert("Match Over Error", "Match ended but final state couldn't be saved as 'completed'. Summary might be incorrect.", [{ text: "OK", onPress: () => navigation.navigate('Home') }]);
        } else if (justTransitionedToSecondInnings) {
            setSelectedStriker(null); 
            setSelectedNonStriker(null); 
            dispatch({ type: 'SET_UI_STATE', payload: UiState.SELECTING_BATSMEN });
            dispatch({ type: 'SET_INNING2_ALERT_SHOWN', payload: false });
        } else {
            const activeInningsState = savedMatchAfterBall.status === 'live_inning1' ? savedMatchAfterBall.firstInnings : savedMatchAfterBall.secondInnings;
            if (outcome === BallOutcome.WICKET && activeInningsState) {
                if (savedMatchAfterBall.lastManStanding && 
                    activeInningsState.strikerId && 
                    activeInningsState.nonStrikerId === null && 
                    activeInningsState.wickets === savedMatchAfterBall.playersPerTeam - 1) {
                  // No UI state change needed here specifically for batsman selection if last man is already set
                } else if (activeInningsState.wickets < (savedMatchAfterBall.lastManStanding ? savedMatchAfterBall.playersPerTeam : savedMatchAfterBall.playersPerTeam -1) ){
                  dispatch({ type: 'SET_UI_STATE', payload: UiState.SELECTING_NEW_BATSMAN });
                }
            }
            
            if (overCompletedThisBall && activeInningsState) {
                dispatch({ type: 'SET_UI_STATE', payload: UiState.SELECTING_NEW_BOWLER });
            }
        }
    } else {
        Alert.alert("Save Error", "Could not save match state after the ball. Match may be out of sync.");
    }
  };

  const handleSelectNewBatsman = async (player: Player) => {
    if (!currentMatch || !innings) return;
    if (nonStriker && nonStriker.id === player.id) {
      Alert.alert("Selection Error", "New batsman cannot be the same as the current Non-Striker.");
      return;
    }
    await updateAndSaveMatch(prevMatch => {
        const updated = JSON.parse(JSON.stringify(prevMatch));
        const activeInnings = updated.status === 'live_inning1' ? updated.firstInnings : updated.secondInnings;
        if (activeInnings) {
            activeInnings.strikerId = player.id; // New batsman takes strike
        }
        return updated;
    });
    setSelectedStriker(player); // Update local UI selection state
    dispatch({ type: 'SET_UI_STATE', payload: UiState.SCORING_ACTIVE }); 
  };

  const handleSelectNewBowler = async (player: Player) => {
    if (!currentMatch || !innings) return;
    if (currentBowler && currentBowler.id === player.id) {
        Alert.alert("Info", "Selecting the same bowler for consecutive overs.");
    }
    
    await updateAndSaveMatch(prevMatch => {
        const updated = JSON.parse(JSON.stringify(prevMatch));
        const activeInnings = updated.status === 'live_inning1' ? updated.firstInnings : updated.secondInnings;
        if (activeInnings) {
            activeInnings.currentBowlerId = player.id;
            activeInnings.currentOverHistory = []; // Clear for new bowler's over
        }
        return updated;
    });
    setSelectedBowler(player); // Update local UI selection state
    dispatch({ type: 'SET_UI_STATE', payload: UiState.SCORING_ACTIVE });
  };

  if (uiState === UiState.LOADING) {
    return (
      <View style={styles.container}>
        <Text>Loading Scoreboard...</Text>
      </View>
    );
  }

  if (uiState === UiState.ERROR_LOADING) {
    return (
      <View style={styles.container}>
        <Text>Error loading match data. Please go back.</Text>
        {/* Alert should have handled navigation, but a fallback UI can be here */}
      </View>
    );
  }

  if (!currentMatch) {
    // This state should ideally be brief or covered by LOADING/ERROR
    // but acts as a safeguard before accessing currentMatch properties.
    return (
      <View style={styles.container}>
        <Text>Waiting for match data initialization...</Text>
      </View>
    );
  }
  
  const currentInningsDisplay = currentMatch.status === 'live_inning1' ? currentMatch.firstInnings : currentMatch.secondInnings;
  const displayBattingTeamName = currentInningsDisplay?.battingTeamName || "Batting";
  const displayBowlingTeamName = currentInningsDisplay?.bowlingTeamName || (currentMatch?.teamAName === displayBattingTeamName ? currentMatch?.teamBName : currentMatch?.teamAName) || "Bowling";

  // UI for when match is active and no player selection is happening
  const renderActiveScoringInterface = () => (
    <View>
      {/* Current Batsmen and Bowler Info */}
      <View style={styles.currentInfo}>
        <Text>
          Striker: {striker ? 
            <>{striker.name}{innings?.strikerId === striker.id ? '*' : ''} (<Text testID="striker-runs">{striker.battingStats.runsScored}</Text>/<Text testID="striker-balls">{striker.battingStats.ballsFaced}</Text>)</>
            : 'N/A'}
        </Text>
        <Text>
          Non-Striker: {nonStriker ? 
            <>{nonStriker.name}{innings?.nonStrikerId === nonStriker.id ? '*' : ''} ({nonStriker.battingStats.runsScored}/{nonStriker.battingStats.ballsFaced})</>
            : 'N/A'}
        </Text>
        <Text>
          Bowler: {currentBowler ? 
            <>{currentBowler.name} ({currentBowler.bowlingStats.wicketsTaken}/{currentBowler.bowlingStats.runsConceded} O: {Math.floor(currentBowler.bowlingStats.ballsBowled/6)}.{currentBowler.bowlingStats.ballsBowled % 6})</>
            : 'N/A'}
        </Text>
      </View>

      {/* Score Display */}
      <View style={styles.scoreDisplayContainer}>
        <View style={styles.scoreRow}>
          <Text style={styles.scoreText} testID="total-score">
            {innings?.score || 0}/{innings?.wickets || 0}
          </Text>
          <Text style={styles.oversText}>
            {innings?.oversCompleted || 0}.{innings?.ballsInCurrentOver || 0}
          </Text>
        </View>
      </View>

      {/* Current Over Display */}
      <View style={styles.currentOverDisplay}>
        <Text>This Over: {innings?.currentOverHistory.join(', ')}</Text>
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        <Text style={styles.controlsTitle}>Record Ball:</Text>
        <View style={styles.buttonRow}>
          {[BallOutcome.DOT, BallOutcome.SINGLE, BallOutcome.DOUBLE, BallOutcome.TRIPLE].map((runsValue) => (
            <Button key={String(runsValue)} title={String(runsValue)} onPress={() => handleBallPlayed(runsValue)} />
          ))}
        </View>
        <View style={styles.buttonRow}>
          {[BallOutcome.FOUR, BallOutcome.SIX, BallOutcome.WIDE, BallOutcome.NO_BALL].map((outcomeType) => (
            <Button key={String(outcomeType)} title={String(outcomeType)} onPress={() => handleBallPlayed(outcomeType)} />
          ))}
        </View>
        <View style={styles.buttonRow}>
          <Button title="Wicket" onPress={() => handleBallPlayed(BallOutcome.WICKET)} color="red" />
        </View>
      </View>
    </View>
  );

  return (
    <ScrollView style={styles.container} keyboardShouldPersistTaps="handled">
      <View style={styles.header}>
        <Text style={styles.headerText}>{currentMatch.teamAName || 'Team A'} vs {currentMatch.teamBName || 'Team B'}</Text>
        <Text style={styles.subHeaderText}>
            {currentMatch.oversPerInnings || 0} Over Match ({currentMatch.playersPerTeam || 0} players/team)
            {currentMatch.secondInnings?.target ? ` | Target: ${currentMatch.secondInnings.target}` : ''}
        </Text>
        {currentMatch.tossWinnerTeamName && currentMatch.decision && 
            <Text style={styles.tossInfo}>{currentMatch.tossWinnerTeamName} won toss & chose to {currentMatch.decision}.</Text>
        }
        {currentMatch.lastManStanding && <Text style={styles.subHeaderTextLMS}>Last Man Standing Enabled</Text>}
      </View>

      {/* Player Selection Modals/Sections */}
      {uiState === UiState.SELECTING_BATSMEN && innings && (
        <View style={styles.selectionContainer}>
          <Text style={styles.selectionTitle}>Select Opening Batsmen</Text>
          <Text style={styles.selectionInfo}>Team: {innings.battingTeamName}</Text>
          
          <Text style={styles.label}>Select Striker:</Text>
          {(battingTeamPlayers)?.map((player: Player) => (
            <Button 
              key={`${player.id}-striker-sel`}
              title={`${player.name}${selectedStriker?.id === player.id ? ' (Striker)' : ''}`}
              onPress={() => handleSelectStriker(player)}
              color={selectedStriker?.id === player.id ? '#4CAF50' : '#007bff'}
              disabled={selectedNonStriker?.id === player.id || player.battingStats.status === 'out'}
            />
          ))}
          <Text style={styles.selectedPlayerText}>Striker: {selectedStriker ? selectedStriker.name : 'Not selected'}</Text>

          <Text style={styles.label}>Select Non-Striker:</Text>
          {(battingTeamPlayers)?.map((player: Player) => (
            <Button 
              key={`${player.id}-nonstriker-sel`}
              title={`${player.name}${selectedNonStriker?.id === player.id ? ' (Non-Striker)' : ''}`}
              onPress={() => handleSelectNonStriker(player)}
              color={selectedNonStriker?.id === player.id ? '#4CAF50' : '#007bff'}
              disabled={selectedStriker?.id === player.id || player.battingStats.status === 'out'}
            />
          ))}
          <Text style={styles.selectedPlayerText}>Non-Striker: {selectedNonStriker ? selectedNonStriker.name : 'Not selected'}</Text>

          {selectedStriker && selectedNonStriker && (
            <Button title="Confirm Batsmen" onPress={handleConfirmBatsmen} color="#28a745" />
          )}
        </View>
      )}

      {uiState === UiState.SELECTING_BOWLER && innings && (
        <View style={styles.selectionContainer}>
          <Text style={styles.selectionTitle}>Select Opening Bowler</Text>
          <Text style={styles.selectionInfo}>Team: {innings.bowlingTeamName}</Text>
          
          {(bowlingTeamPlayers)?.map((player: Player) => (
            <Button 
              key={`${player.id}-bowler-sel`}
              title={`${player.name}${selectedBowler?.id === player.id ? ' (Selected)' : ''}`}
              onPress={() => handleSelectBowler(player)}
              color={selectedBowler?.id === player.id ? '#4CAF50' : '#007bff'}
            />
          ))}
          <Text style={styles.selectedPlayerText}>Bowler: {selectedBowler ? selectedBowler.name : 'Not selected'}</Text>

          {selectedBowler && (
            <Button title="Confirm Bowler" onPress={handleConfirmBowler} color="#28a745" />
          )}
        </View>
      )}

      {uiState === UiState.SELECTING_NEW_BATSMAN && innings && (
        <View style={styles.selectionContainer}>
          <Text style={styles.selectionTitle}>Select Next Batsman</Text>
          <Text style={styles.selectionInfo}>Team: {innings.battingTeamName}</Text>
          {
            (battingTeamPlayers)
              ?.filter(p => p.battingStats.status !== 'out' && p.id !== innings?.nonStrikerId)
              .map((player: Player) => (
                <Button 
                  key={`${player.id}-newbatsman-sel`}
                  title={player.name}
                  onPress={() => handleSelectNewBatsman(player)}
                  color={'#007bff'}
                />
              ))
          }
          {(battingTeamPlayers)?.filter(p => p.battingStats.status !== 'out' && p.id !== innings?.nonStrikerId).length === 0 && (
            <Text>No more available batsmen.</Text>
          )}
        </View>
      )}

      {uiState === UiState.SELECTING_NEW_BOWLER && innings && (
        <View style={styles.selectionContainer}>
          <Text style={styles.selectionTitle}>Select Bowler for Next Over</Text>
          <Text style={styles.selectionInfo}>Team: {innings.bowlingTeamName}</Text>
          {
            (bowlingTeamPlayers)
              ?.map((player: Player) => (
                <Button 
                  key={`${player.id}-newbowler-sel`}
                  title={`${player.name}${currentBowler?.id === player.id ? ' (Current)' : ''}`}
                  onPress={() => handleSelectNewBowler(player)}
                  color={'#007bff'}
                  disabled={player.id === currentBowler?.id && innings.ballsInCurrentOver > 0 && innings.ballsInCurrentOver < 6} // Prevent selecting same bowler mid-over change
                />
              ))
          }
        </View>
      )}

      {/* Scoring interface - show only after ALL selections are made and match is live */}
      {uiState === UiState.SCORING_ACTIVE && currentMatch && innings && striker && currentBowler && (
        renderActiveScoringInterface()
      )}
      { uiState === UiState.MATCH_COMPLETE && innings &&
        <View style={styles.matchResult}>
            <Text style={styles.matchResultTitle}>Match Completed!</Text>
            <Text style={styles.matchResultText}>{currentMatch.resultDescription}</Text>
            <Button title="Back to Home" onPress={() => navigation.navigate('Home')} />
        </View>
      }
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 10,
  },
  header: {
    alignItems: 'center',
    marginBottom: 10, // Reduced margin
  },
  headerText: {
    fontSize: 22, // Slightly smaller
    fontWeight: 'bold',
  },
  subHeaderText: {
    fontSize: 16, // Slightly smaller
    textAlign: 'center',
  },
  subHeaderTextLMS: { 
    fontSize: 14,
    fontStyle: 'italic',
    color: '#555',
    marginTop: 2,
  },
  scoreCard: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 10, // Reduced margin
    padding: 8, // Reduced padding
    backgroundColor: '#f0f0f0',
    borderRadius: 5,
  },
  scoreText: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  oversText: {
    fontSize: 18,
  },
  currentInfo: {
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: 10, // Reduced margin
    paddingHorizontal: 8, // Reduced padding
    paddingVertical: 4, // Reduced padding
    backgroundColor: '#f9f9f9',
    borderRadius: 5,
  },
  currentOverDisplay: {
    marginBottom: 10, // Reduced margin
    padding: 8, // Reduced padding
    backgroundColor: '#e9e9e9',
    borderRadius: 5,
    alignItems: 'center', // Center text
  },
  controls: {
    marginTop: 10, // Reduced margin
  },
  controlsTitle: {
    fontSize: 16, // Slightly smaller
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8, // Reduced margin
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8, // Reduced margin
  },
  tossInfo: { 
    fontSize: 14, // Slightly smaller
    color: '#4A90E2', 
    marginTop: 3,
    marginBottom: 3,
    fontWeight: '500',
    textAlign: 'center',
  },
  selectionContainer: { 
    padding: 10, // Reduced padding
    marginVertical: 8, // Reduced margin
    backgroundColor: '#e0e0e0',
    borderRadius: 8,
    alignItems: 'center',
  },
  selectionTitle: { 
    fontSize: 18, // Slightly smaller
    fontWeight: 'bold',
    marginBottom: 8, // Reduced margin
  },
  selectionInfo: { 
    fontSize: 14, // Slightly smaller
    marginBottom: 8, // Reduced margin
  },
  label: { 
    fontSize: 14, // Slightly smaller
    fontWeight: '500',
    marginTop: 8, // Reduced margin
    marginBottom: 4, // Reduced margin
  },
  selectedPlayerText: { 
    fontSize: 14, // Slightly smaller
    marginVertical: 4, // Reduced margin
    fontWeight: 'bold',
    color: '#333',
  },
  matchResult: {
    padding: 20,
    alignItems: 'center',
    marginTop: 20,
  },
  matchResultTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  matchResultText: {
    fontSize: 18,
    marginBottom: 20,
    textAlign: 'center',
  },
  batsmanDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  batsmanRole: {
    fontWeight: 'bold',
  },
  playerName: {
    marginLeft: 5,
  },
  strikerIndicator: {
    color: '#4CAF50',
    fontWeight: 'bold',
  },
  playerStats: {
    marginTop: 2,
  },
  scoreDisplayContainer: {
    marginTop: 10,
  },
  scoreRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
}); 