import React from 'react';
import { View, Text, StyleSheet, Pressable, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { ScreenNavigationProp } from '../navigation/AppNavigator'; // Adjusted path
// import { PlusCircle, History, Zap, Edit, Archive } from 'lucide-react-native';

const ICON_COLOR = "#4f46e5"; // A nice primary color, adjust as needed
// const ICON_SIZE = 24;

// Define the type for the navigation prop specifically for this screen
type HomeScreenNavigationProp = ScreenNavigationProp<'Home'>;

export default function HomeScreen() {
  const navigation = useNavigation<HomeScreenNavigationProp>();

  return (
    <View style={styles.container}>
      <View style={styles.headerCard}>
        <Image 
          source={{ uri: 'https://picsum.photos/seed/cricketpitch/120/120' }} 
          style={styles.logoImage} 
        />
        <Text style={styles.title}>Welcome to GullyScore!</Text>
        <Text style={styles.description}>
          Your companion for scoring gully cricket matches with ease and fun.
        </Text>
        <View style={styles.buttonGroup}>
          <Pressable style={[styles.button, styles.accentButton]} onPress={() => navigation.navigate('ConfigureMatch')}>
            {/* <PlusCircle color="white" size={ICON_SIZE} style={styles.buttonIcon} /> */}
            <Text style={[styles.buttonText, styles.accentButtonText]}>Start New Match</Text>
          </Pressable>
          <Pressable style={[styles.button, styles.outlineButton]} onPress={() => navigation.navigate('MatchHistory')}>
            {/* <History color={ICON_COLOR} size={ICON_SIZE} style={styles.buttonIcon} /> */}
            <Text style={[styles.buttonText, styles.outlineButtonText]}>View Match History</Text>
          </Pressable>
        </View>
      </View>

      <View style={styles.featuresSection}>
        <Text style={styles.featuresTitle}>Why GullyScore?</Text>
        <View style={styles.featuresGrid}>
          <View style={styles.featureCard}>
            {/* <Zap color={ICON_COLOR} size={40} style={styles.featureIcon} /> */}
            <Text style={styles.featureCardTitle}>Quick Setup</Text>
            <Text style={styles.featureCardText}>Start matches in seconds with team names and overs.</Text>
          </View>
          <View style={styles.featureCard}>
            {/* <Edit color={ICON_COLOR} size={40} style={styles.featureIcon} /> */}
            <Text style={styles.featureCardTitle}>Real-time Scoring</Text>
            <Text style={styles.featureCardText}>Input scores, wickets, and extras as they happen.</Text>
          </View>
          <View style={styles.featureCard}>
            {/* <Archive color={ICON_COLOR} size={40} style={styles.featureIcon} /> */}
            <Text style={styles.featureCardTitle}>Match History</Text>
            <Text style={styles.featureCardText}>Save and revisit your completed matches anytime.</Text>
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f3f4f6', // Light gray background, similar to Tailwind's bg-gray-100
    paddingVertical: 20,
  },
  headerCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 24,
    alignItems: 'center',
    marginHorizontal: 16,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5, // for Android shadow
  },
  logoImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
    borderColor: ICON_COLOR,
    marginBottom: 16,
  },
  title: {
    fontSize: 26, // Adjusted size for mobile
    fontWeight: 'bold',
    color: '#1f2937', // Darker gray, like text-gray-800
    textAlign: 'center',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: '#4b5563', // Medium gray, like text-gray-600
    textAlign: 'center',
    marginBottom: 24,
  },
  buttonGroup: {
    width: '100%',
    alignItems: 'center', // Center buttons if they stack
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    marginBottom: 12, // Space between buttons
    width: '90%', // Make buttons take good width
    minHeight: 50, // Ensure buttons have a good tap area
  },
  accentButton: {
    backgroundColor: ICON_COLOR,
  },
  outlineButton: {
    borderColor: ICON_COLOR,
    borderWidth: 1,
    backgroundColor: 'transparent',
  },
  buttonIcon: {
    marginRight: 10,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600', // semibold
  },
  accentButtonText: {
    color: 'white',
  },
  outlineButtonText: {
    color: ICON_COLOR,
  },
  featuresSection: {
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  featuresTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  featuresGrid: {
    width: '100%',
    // For a 1-column layout on mobile, items will stack. 
    // For a grid, you might use flexWrap: 'wrap' and assign widths to featureCard if you don't want to use FlatList with numColumns
  },
  featureCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16, // Slightly smaller padding for feature cards
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2.22,
    elevation: 3,
  },
  featureIcon: {
    marginBottom: 12,
  },
  featureCardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  featureCardText: {
    fontSize: 14,
    color: '#4b5563',
    textAlign: 'center',
  },
});
