import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Button, Alert } from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { RootStackParamList, ScreenNavigationProp, TossScreenParams } from '../navigation/AppNavigator'; // Adjust path
import { Match } from '../types/matchTypes'; // Import Match type
import { loadMatch, saveMatch } from '../utils/storage'; // Import storage functions

// Define the type for the route params this screen receives
type TossScreenRouteProp = RouteProp<RootStackParamList, 'Toss'>;
// Define the type for this screen's navigation prop
type TossScreenNavigationProp = ScreenNavigationProp<'Toss'>;

export default function TossScreen() {
  const route = useRoute<TossScreenRouteProp>();
  const navigation = useNavigation<TossScreenNavigationProp>();
  
  const { matchId } = route.params; // matchId is now the only param

  const [currentMatch, setCurrentMatch] = useState<Match | null>(null);
  const [tossWinnerIdentifier, setTossWinnerIdentifier] = useState<'Team A' | 'Team B' | null>(null);
  const [tossMade, setTossMade] = useState(false);
  const [winnerDecision, setWinnerDecision] = useState<'Bat' | 'Bowl' | null>(null);

  useEffect(() => {
    const fetchMatchData = async () => {
      const loadedMatch = await loadMatch(matchId);
      if (loadedMatch) {
        setCurrentMatch(loadedMatch);
      } else {
        Alert.alert("Error", "Could not load match data. Please go back and try again.");
        // Optionally navigate back: navigation.goBack();
      }
    };
    fetchMatchData();
  }, [matchId]);

  const getTossWinnerName = () => {
    if (!currentMatch) return null;
    if (tossWinnerIdentifier === 'Team A') return currentMatch.teamAName;
    if (tossWinnerIdentifier === 'Team B') return currentMatch.teamBName;
    return null;
  };

  const handleCoinToss = () => {
    if (!currentMatch) return;
    const resultIdentifier = Math.random() < 0.5 ? 'Team A' : 'Team B';
    setTossWinnerIdentifier(resultIdentifier);
    setTossMade(true);
    const winnerName = resultIdentifier === 'Team A' ? currentMatch.teamAName : currentMatch.teamBName;
    Alert.alert("Coin Tossed!", `${winnerName} won the toss!`);
  };

  const handleChoice = async (choice: 'Bat' | 'Bowl') => {
    if (!tossWinnerIdentifier || !currentMatch) return; 
    setWinnerDecision(choice);

    let battingTeamName: string;
    let bowlingTeamName: string;
    const tossWinnerActualName = getTossWinnerName();
    if (!tossWinnerActualName) return; 

    if (tossWinnerIdentifier === 'Team A') {
      battingTeamName = choice === 'Bat' ? currentMatch.teamAName : currentMatch.teamBName;
      bowlingTeamName = choice === 'Bat' ? currentMatch.teamBName : currentMatch.teamAName;
    } else { 
      battingTeamName = choice === 'Bat' ? currentMatch.teamBName : currentMatch.teamAName;
      bowlingTeamName = choice === 'Bat' ? currentMatch.teamAName : currentMatch.teamBName;
    }

    const updatedMatch: Match = {
      ...currentMatch,
      tossWinnerTeamName: tossWinnerActualName,
      decision: choice,
      status: 'live_inning1', // Match is now live, starting inning 1
      updatedAt: Date.now(),
      firstInnings: {
        battingTeamName,
        bowlingTeamName,
        score: 0,
        wickets: 0,
        oversCompleted: 0,
        ballsInCurrentOver: 0,
        currentOverHistory: [],
        runsConcededThisOverMaiden: 0,
        timeline: [],
        // strikerId, nonStrikerId, currentBowlerId will be set in MatchScoringScreen
      }
    };

    const success = await saveMatch(updatedMatch);
    if (success) {
      setCurrentMatch(updatedMatch); // Update local state as well
      navigation.navigate('MatchScoring', { matchId: currentMatch.id });
    } else {
      Alert.alert("Storage Error", "Could not update match details. Please try again.");
    }
  };

  const displayTossWinnerName = getTossWinnerName();

  if (!currentMatch) {
    return (
      <View style={styles.container}>
        <Text>Loading match details...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Time for the Toss!</Text>
      <Text style={styles.matchInfo}>{currentMatch.teamAName} vs {currentMatch.teamBName}</Text>
      
      {!tossMade && (
        <View style={styles.tossButtonContainer}>
          <Button title="Flip Coin" onPress={handleCoinToss} color="#28a745" />
        </View>
      )}

      {tossMade && tossWinnerIdentifier && !winnerDecision && displayTossWinnerName && (
        <View style={styles.choiceContainer}>
          <Text style={styles.tossWinner}>{displayTossWinnerName} won the toss!</Text>
          <Text style={styles.choicePrompt}>What will {displayTossWinnerName} choose?</Text>
          <View style={styles.choiceButtons}>
            <Button title="Bat First" onPress={() => handleChoice('Bat')} color="#007bff" />
            <Button title="Bowl First" onPress={() => handleChoice('Bowl')} color="#ffc107" />
          </View>
        </View>
      )}

      {winnerDecision && tossWinnerIdentifier && displayTossWinnerName && (
        <Text style={styles.finalDecision}>
          {displayTossWinnerName} chose to {winnerDecision.toLowerCase()} first.
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f0f8ff',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  matchInfo: {
    fontSize: 18,
    color: '#555',
    marginBottom: 30,
  },
  tossButtonContainer: {
    width: '80%',
    marginBottom: 20,
  },
  choiceContainer: {
    alignItems: 'center',
    width: '100%',
  },
  tossWinner: {
    fontSize: 22,
    fontWeight: '600',
    color: '#17a2b8',
    marginBottom: 10,
  },
  choicePrompt: {
    fontSize: 18,
    color: '#333',
    marginBottom: 20,
  },
  choiceButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '90%',
  },
  finalDecision: {
    fontSize: 20,
    fontWeight: '500',
    color: 'green',
    marginTop: 30,
  },
}); 