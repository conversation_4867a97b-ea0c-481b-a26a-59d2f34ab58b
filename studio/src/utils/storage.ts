import AsyncStorage from '@react-native-async-storage/async-storage';
import { Match } from '../types/matchTypes';

const MATCHES_KEY = 'matches';

// Helper to get all matches or an empty array
const getAllMatches = async (): Promise<Match[]> => {
  try {
    const jsonValue = await AsyncStorage.getItem(MATCHES_KEY);
    return jsonValue != null ? JSON.parse(jsonValue) : [];
  } catch (e) {
    console.error("Failed to load matches.", e);
    return [];
  }
};

// Save or update a single match
export const saveMatch = async (matchToSave: Match): Promise<boolean> => {
  try {
    const matches = await getAllMatches();
    const matchIndex = matches.findIndex(m => m.id === matchToSave.id);

    if (matchIndex > -1) {
      // Update existing match
      matches[matchIndex] = matchToSave;
    } else {
      // Add new match
      matches.push(matchToSave);
    }
    const jsonValue = JSON.stringify(matches);
    await AsyncStorage.setItem(MATCHES_KEY, jsonValue);
    console.log(`Match ${matchToSave.id} saved successfully.`);
    return true;
  } catch (e) {
    console.error("Failed to save match.", e);
    return false;
  }
};

// Load a single match by ID
export const loadMatch = async (matchId: string): Promise<Match | null> => {
  try {
    const matches = await getAllMatches();
    const match = matches.find(m => m.id === matchId);
    return match || null;
  } catch (e) {
    console.error(`Failed to load match ${matchId}.`, e);
    return null;
  }
};

// Load all matches (mainly for listing them)
export const loadAllMatches = async (): Promise<Match[]> => {
  return await getAllMatches();
};

// Delete a single match by ID
export const deleteMatch = async (matchId: string): Promise<boolean> => {
  try {
    let matches = await getAllMatches();
    matches = matches.filter(m => m.id !== matchId);
    const jsonValue = JSON.stringify(matches);
    await AsyncStorage.setItem(MATCHES_KEY, jsonValue);
    console.log(`Match ${matchId} deleted successfully.`);
    return true;
  } catch (e) {
    console.error(`Failed to delete match ${matchId}.`, e);
    return false;
  }
};

// Clear all matches (for testing or reset)
export const clearAllMatches = async (): Promise<boolean> => {
  try {
    await AsyncStorage.removeItem(MATCHES_KEY);
    console.log("All matches cleared.");
    return true;
  } catch (e) {
    console.error("Failed to clear matches.", e);
    return false;
  }
}; 