import AsyncStorage from '@react-native-async-storage/async-storage';
import { saveMatch, loadMatch, loadAllMatches, deleteMatch, clearAllMatches } from '../storage';
import { Match, Player } from '../../types/matchTypes';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(() => Promise.resolve()),
  getItem: jest.fn(() => Promise.resolve(null)),
  removeItem: jest.fn(() => Promise.resolve()),
  getAllKeys: jest.fn(() => Promise.resolve([])),
  multiGet: jest.fn(() => Promise.resolve([])),
}));

// Define MatchStatus locally if preferred, or use string literals directly
const MatchStatus = {
  PENDING: 'pending',
  LIVE_INNING1: 'live_inning1',
  LIVE_INNING2: 'live_inning2',
  COMPLETED: 'completed',
  ABANDONED: 'abandoned',
} as const; // Restored 'as const'

describe('Storage Utilities', () => {
  const mockPlayer: Player = {
    id: 'player1',
    name: 'Test Player',
    battingStats: { runsScored: 0, ballsFaced: 0, fours: 0, sixes: 0, status: 'did not bat' },
    bowlingStats: { ballsBowled: 0, runsConceded: 0, wicketsTaken: 0, maidensBowled: 0, widesBowled: 0, noBallsBowled: 0 },
  };

  const mockMatch1: Match = {
    id: 'match-1',
    teamAName: 'Team A', 
    teamBName: 'Team B',
    oversPerInnings: 10,
    playersPerTeam: 2,
    lastManStanding: false,
    status: MatchStatus.PENDING,
    createdAt: new Date('2024-01-01T10:00:00Z').getTime(),
    updatedAt: new Date('2024-01-01T10:00:00Z').getTime(),
    teamAPlayers: [mockPlayer],
    teamBPlayers: [mockPlayer],
    firstInnings: undefined,
    secondInnings: undefined,
  };

  const mockMatch2: Match = {
    id: 'match-2',
    teamAName: 'Team C',
    teamBName: 'Team D',
    oversPerInnings: 5,
    playersPerTeam: 1,
    lastManStanding: true,
    status: MatchStatus.COMPLETED,
    createdAt: new Date('2024-01-02T12:00:00Z').getTime(),
    updatedAt: new Date('2024-01-02T12:30:00Z').getTime(),
    teamAPlayers: [mockPlayer],
    teamBPlayers: [mockPlayer],
    firstInnings: undefined,
    secondInnings: undefined,
    matchWinnerTeamName: 'Team C',
    resultDescription: 'Team C won by 10 runs',
  };

  const MATCHES_KEY = 'matches';

  beforeEach(() => {
    (AsyncStorage.setItem as jest.Mock).mockClear().mockResolvedValue(undefined);
    (AsyncStorage.getItem as jest.Mock).mockClear().mockResolvedValue(JSON.stringify([]));
    (AsyncStorage.removeItem as jest.Mock).mockClear().mockResolvedValue(undefined);
  });

  describe('saveMatch', () => {
    it('should add a new match if it does not exist', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify([]));
      await saveMatch(mockMatch1);
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        MATCHES_KEY,
        JSON.stringify([mockMatch1])
      );
    });

    it('should update an existing match', async () => {
      const updatedMockMatch1 = { ...mockMatch1, status: MatchStatus.LIVE_INNING1, updatedAt: new Date().getTime() };
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify([mockMatch1]));
      await saveMatch(updatedMockMatch1);
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        MATCHES_KEY,
        JSON.stringify([updatedMockMatch1])
      );
    });

    it('should add to existing list of matches', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify([mockMatch1]));
      await saveMatch(mockMatch2);
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(
        MATCHES_KEY,
        JSON.stringify([mockMatch1, mockMatch2])
      );
    });

    it('should return true on successful save', async () => {
      const result = await saveMatch(mockMatch1);
      expect(result).toBe(true);
    });

    it('should return false on save failure', async () => {
      (AsyncStorage.setItem as jest.Mock).mockRejectedValueOnce(new Error('Save failed'));
      const result = await saveMatch(mockMatch1);
      expect(result).toBe(false);
    });
  });

  describe('loadMatch', () => {
    it('should return the correct match if found', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify([mockMatch1, mockMatch2]));
      const result = await loadMatch('match-1');
      expect(AsyncStorage.getItem).toHaveBeenCalledWith(MATCHES_KEY);
      expect(result).toEqual(mockMatch1);
    });

    it('should return null if match not found', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify([mockMatch1, mockMatch2]));
      const result = await loadMatch('non-existent-id');
      expect(result).toBeNull();
    });

    it('should return null if storage is empty or fails to parse', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(null);
      let result = await loadMatch('match-1');
      expect(result).toBeNull();
      
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce('invalid json');
      result = await loadMatch('match-1');
      expect(result).toBeNull(); 
    });
  });

  describe('loadAllMatches', () => {
    it('should return all matches from storage', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify([mockMatch1, mockMatch2]));
      const result = await loadAllMatches();
      expect(AsyncStorage.getItem).toHaveBeenCalledWith(MATCHES_KEY);
      expect(result).toEqual([mockMatch1, mockMatch2]);
    });

    it('should return an empty array if no matches in storage', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify([]));
      const result = await loadAllMatches();
      expect(result).toEqual([]);
    });

    it('should return an empty array if storage getItem returns null', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(null);
      const result = await loadAllMatches();
      expect(result).toEqual([]);
    });
  });

  describe('deleteMatch', () => {
    it('should remove the specified match and return true', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify([mockMatch1, mockMatch2]));
      const result = await deleteMatch('match-1');
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(MATCHES_KEY, JSON.stringify([mockMatch2]));
      expect(result).toBe(true);
    });

    it('should do nothing and return true if match to delete is not found', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify([mockMatch2]));
      const result = await deleteMatch('match-1');
      expect(AsyncStorage.setItem).toHaveBeenCalledWith(MATCHES_KEY, JSON.stringify([mockMatch2]));
      expect(result).toBe(true);
    });

    it('should return false on delete failure', async () => {
      (AsyncStorage.setItem as jest.Mock).mockRejectedValueOnce(new Error('Delete failed'));
      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify([mockMatch1]));
      const result = await deleteMatch('match-1');
      expect(result).toBe(false);
    });
  });

  describe('clearAllMatches', () => {
    it('should call AsyncStorage.removeItem with the correct key and return true', async () => {
      const result = await clearAllMatches();
      expect(AsyncStorage.removeItem).toHaveBeenCalledWith(MATCHES_KEY);
      expect(result).toBe(true);
    });

    it('should return false on failure', async () => {
      (AsyncStorage.removeItem as jest.Mock).mockRejectedValueOnce(new Error('Clear failed'));
      const result = await clearAllMatches();
      expect(result).toBe(false);
    });
  });
}); 